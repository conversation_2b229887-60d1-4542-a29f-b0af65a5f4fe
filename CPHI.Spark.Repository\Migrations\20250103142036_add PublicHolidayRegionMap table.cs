﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class addPublicHolidayRegionMaptable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PublicHolidayRegionMaps",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PublicHolidayId = table.Column<int>(type: "int", nullable: false),
                    PublicHolidayRegionId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PublicHolidayRegionMaps", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PublicHolidayRegionMaps_PublicHolidayRegions_PublicHolidayRegionId",
                        column: x => x.PublicHolidayRegionId,
                        principalTable: "PublicHolidayRegions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PublicHolidayRegionMaps_PublicHolidays_PublicHolidayId",
                        column: x => x.PublicHolidayId,
                        principalTable: "PublicHolidays",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayId",
                table: "PublicHolidayRegionMaps",
                column: "PublicHolidayId");

            migrationBuilder.CreateIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayRegionId",
                table: "PublicHolidayRegionMaps",
                column: "PublicHolidayRegionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PublicHolidayRegionMaps");
        }
    }
}
