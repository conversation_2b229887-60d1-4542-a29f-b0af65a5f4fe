﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class AdvertDetailsForCompetitorList
   {
      public AdvertDetailsForCompetitorList(VehicleAdvertDetail ad)
      {
         AdSiteName = ad.AdSiteName;
         OdometerReading = ad.OdometerReading;
         FirstRegisteredDate = ad.FirstRegisteredDate;
         AdvertisedPrice = (int)ad.AdvertisedPrice;
         VehicleReg = ad.VehicleReg;
         ImageUrl = ad.ImageUrl;
         WebSiteSearchIdentifier = ad.WebSiteSearchIdentifier;
         IsTradePricing = ad.IsTradePricing;
         TradeMarginPercentage = ad.TradeMarginPercentage;
         TradeMarginAmount = ad.TradeMarginAmount;
         Derivative = ad.Derivative;

         FuelType = ad.FuelType;
         BodyType = ad.BodyType;
         Doors = ad.Doors.ToString();
         TransmissionType = ad.TransmissionType;
         Trim = ad.Trim;
        }
      public AdvertDetailsForCompetitorList(VehicleAdvertWithRating ad)
      {
         AdSiteName = ad.RetailerSiteName;
         OdometerReading = ad.OdometerReading;
         FirstRegisteredDate = ad.FirstRegisteredDate;
         AdvertisedPrice = ad.AdvertisedPrice;
         VehicleReg = ad.VehicleReg;
         ImageUrl = ad.ImageURL;
         WebSiteSearchIdentifier = ad.WebSiteSearchIdentifier;

         IsTradePricing = ad.IsTradePricing;
         TradeMarginPercentage = ad.TradeMarginPercentage;
         TradeMarginAmount = ad.TradeMarginAmount;
         Derivative = ad.Derivative;

         FuelType = ad.FuelType;
         BodyType = ad.BodyType;
         Doors = ad.Doors.ToString();
         TransmissionType = ad.TransmissionType;
         Trim = ad.Trim;
      }

      public string AdSiteName { get; set; }
      public int? OdometerReading { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      public int AdvertisedPrice { get; set; }
      public string VehicleReg { get; set; }
      public string ImageUrl { get; set; }
      public string WebSiteSearchIdentifier { get; set; }
      public bool? IsTradePricing { get; set; }
      public decimal? TradeMarginPercentage { get; set; }
      public int? TradeMarginAmount { get; set; }
      public string Derivative { get; set; }

      public string FuelType { get; set; }
      public string BodyType { get; set; }
      public string Doors { get; set; }
      public string TransmissionType { get; set; }
      public string Trim { get; set; }
   }


}
