using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model.ViewModels;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PublicHolidaysController : ControllerBase
    {
        private readonly IPublicHolidaysService publicHolidaysService;

        public PublicHolidaysController(IPublicHolidaysService publicHolidaysService)
        {
            this.publicHolidaysService = publicHolidaysService;
        }

        [HttpGet]
        [Route("AddPublicHoliday")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> AddPublicHoliday(DateTime date, string regionName)
        {
            try
            {
                await publicHolidaysService.AddPublicHolidayAsync(date, regionName);
                return Ok(new { Success = true, Message = "Public holiday added successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPut]
        [Route("EditPublicHoliday/{id}")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> EditPublicHoliday(int id, DateTime date, string regionName)
        {
            try
            {
                await publicHolidaysService.EditPublicHolidayAsync(id, date, regionName);
                return Ok(new { Success = true, Message = "Public holiday updated successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete]
        [Route("RemovePublicHoliday/{id}")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> RemovePublicHoliday(int id)
        {
            try
            {
                await publicHolidaysService.RemovePublicHolidayAsync(id);
                return Ok(new { Success = true, Message = "Public holiday removed successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpGet]
        [Route("GetPublicHolidays")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
        {
            return await publicHolidaysService.GetPublicHolidays();
        }

    }
}
