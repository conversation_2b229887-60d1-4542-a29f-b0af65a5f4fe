using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PublicHolidaysController : ControllerBase
    {
        private readonly IPublicHolidaysService publicHolidaysService;

        public PublicHolidaysController(IPublicHolidaysService publicHolidaysService)
        {
            this.publicHolidaysService = publicHolidaysService;
        }

        [HttpPost]
        [Route("AddPublicHoliday")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> AddPublicHoliday([FromBody] PublicHolidayRequest request)
        {
            try
            {
                var result = await publicHolidaysService.AddPublicHoliday(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPut]
        [Route("UpdatePublicHoliday/{id}")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> UpdatePublicHoliday(int id, [FromBody] PublicHolidayRequest request)
        {
            try
            {
                var result = await publicHolidaysService.UpdatePublicHoliday(id, request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete]
        [Route("DeletePublicHoliday/{id}")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<IActionResult> DeletePublicHoliday(int id)
        {
            try
            {
                var result = await publicHolidaysService.DeletePublicHoliday(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpGet]
        [Route("GetPublicHolidays")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
        {
            return await publicHolidaysService.GetPublicHolidays();
        }

        [HttpGet]
        [Route("GetRegions")]
        [Authorize(Roles = "SysAdministrator")]
        public async Task<List<PublicHolidayRegion>> GetRegions()
        {
            return await publicHolidaysService.GetRegions();
        }

    }
}
