export interface PublicHolidayRequest {
  date: Date;
  dealerGroupId: number;
  regionIds: number[];
}

export interface PublicHolidayResponse {
  id: number;
  date: Date;
  dealerGroupId: number;
  regionIds: number[];
  success: boolean;
  message: string;
}

export interface PublicHolidayRegion {
  id: number;
  region: string;
}

export interface PublicHolidayViewModel {
  id: number;
  date: Date;
  dealerGroupId: number;
  regionNames: string[];
  regionIds: number[];
}
