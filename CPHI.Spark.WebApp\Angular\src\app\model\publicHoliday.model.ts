export interface PublicHolidayRequest {
  date: Date;
  regionIds: number[];
}

export interface PublicHolidayResponse {
  id: number;
  date: Date;
  dealerGroupId: number;
  regionIds: number[];
  success: boolean;
  message: string;
}

export interface PublicHolidayRegion {
  Id: number;
  Region: string;
}

export interface PublicHolidayViewModel {
  id: number;
  date: Date;
  dealerGroupId: number;
  regionNames: string[];
  regionIds: number[];
}
