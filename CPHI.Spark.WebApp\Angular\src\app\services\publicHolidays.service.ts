import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PublicHolidayRequest, PublicHolidayResponse, PublicHolidayRegion } from '../model/publicHoliday.model';

@Injectable({
  providedIn: 'root'
})
export class PublicHolidaysService {

  constructor(private http: HttpClient) { }

  getPublicHolidays(): Observable<PublicHolidayResponse[]> {
    return this.http.get<PublicHolidayResponse[]>('/api/PublicHolidays/GetPublicHolidays');
  }

  addPublicHoliday(request: PublicHolidayRequest): Observable<PublicHolidayResponse> {
    return this.http.post<PublicHolidayResponse>('/api/PublicHolidays/AddPublicHoliday', request);
  }

  updatePublicHoliday(id: number, request: PublicHolidayRequest): Observable<PublicHolidayResponse> {
    return this.http.put<PublicHolidayResponse>(`/api/PublicHolidays/UpdatePublicHoliday/${id}`, request);
  }

  deletePublicHoliday(id: number): Observable<PublicHolidayResponse> {
    return this.http.delete<PublicHolidayResponse>(`/api/PublicHolidays/DeletePublicHoliday/${id}`);
  }

  getRegions(): Observable<PublicHolidayRegion[]> {
    return this.http.get<PublicHolidayRegion[]>('/api/PublicHolidays/GetRegions');
  }
}
