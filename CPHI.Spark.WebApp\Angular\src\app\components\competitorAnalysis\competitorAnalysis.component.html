
<div id="competitorsTable" *ngIf="service.params" [ngClass]="{ 'inModal': inModal }"> <!-- let parent determine height -->

   <div class="d-flex align-items-center pb-2 flex-wrap grid-gap-10">
      <div class="flex-shrink-1 btn btn-primary" (click)="toggleShowFilterMenu()">
         <i class="fa fa-bars"></i>
      </div>

  

      <div>
         <!-- Choose table or blobs buttons, only if we are needing to choose between then -->
         <div class="buttonGroup" *ngIf="!showBlobsAndTableTogether()" style="display: inline-block">
            <button
               class="btn btn-primary"
               [ngClass]="{ active: !showBlobs }"
               ngbPopover="Table"
               triggers="mouseenter:mouseleave"
               container="body"
               (click)="showBlobs = false"
            >
               <i class="fas fa-table"></i>
            </button>
            <button
               *ngFor="let field of chartXYFields"
               class="btn btn-primary"
               [ngbPopover]="'Chart - ' + field.xPretty + ' and ' + field.yPretty"
               triggers="mouseenter:mouseleave"
               container="body"
               [ngClass]="{ active: chartData === field && showBlobs }"
               (click)="showChartView(field)"
            >
               <i class="fas fa-chart-scatter"></i>
            </button>
         </div>

         <button *ngIf="!showBlobs" id="scrollToVehicle" class="btn btn-primary" (click)="scrollToThisVehicle()">
            <i class="fas fa-down-to-line"></i>
         </button>

         <button *ngIf="!inModal && !autoPriceInsightsService.isSmallScreen" class="btn btn-primary"
                 ngbPopover="Open in full size window"
                 triggers="mouseenter:mouseleave"
                 container="body"
                 (click)="openInModal()">
            <i class="fas fa-search-plus"></i>
         </button>
      </div>
   </div>

   <div class="d-flex w-100 flex-grow-1" style="overflow-y: auto; flex-direction: row">
      <div *ngIf="showFilterMenu" id="filterContainer">
         <div style="overflow-y: auto; overflow-x: hidden">
            <div class="mb-2">
               <span class="plate-label">From</span>
               <div ngbDropdown dropright class="d-inline-block" container="body">
                  <button class="btn btn-primary" ngbDropdownToggle>{{ selectedMinPlate }} reg</button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                     <button
                        *ngFor="let plate of plateChoices"
                        ngbDropdownToggle
                        class="manualToggleCloseItem"
                        ngbDropdownItem
                        (click)="selectMinPlate(plate)"
                     >
                        {{ plate }} reg
                     </button>
                  </div>
               </div>
            </div>

            <!-- Plates -->
            <div class="mb-2">
               <span class="plate-label">To</span>
               <div ngbDropdown dropright class="d-inline-block" container="body">
                  <button class="btn btn-primary" ngbDropdownToggle>{{ selectedMaxPlate }} reg</button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                     <button
                        *ngFor="let plate of plateChoices"
                        ngbDropdownToggle
                        class="manualToggleCloseItem"
                        ngbDropdownItem
                        (click)="selectMaxPlate(plate)"
                     >
                        {{ plate }} reg
                     </button>
                  </div>
               </div>
            </div>

            <!-- Radius -->
            <div class="mb-2">
               <span class="plate-label">Radius</span>
               <div class="d-inline-block" ngbDropdown dropright container="body">
                  <button class="btn btn-primary btn-inline-block" ngbDropdownToggle>
                     {{ getDistanceLabel() }}
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                     <button
                        *ngFor="let radius of distances"
                        ngbDropdownToggle
                        class="manualToggleCloseItem"
                        ngbDropdownItem
                        [ngClass]="{ active: selectedRadius === radius }"
                        (click)="selectRadius(radius)"
                     >
                        {{ radius }} {{ radius === 1 ? "mile" : "miles" }}
                     </button>
                     <button
                        ngbDropdownToggle
                        class="manualToggleCloseItem"
                        ngbDropdownItem
                        [ngClass]="{ active: selectedRadius === 1000 }"
                        (click)="selectRadius(1000)"
                     >
                        National
                     </button>
                  </div>

                  <span *ngIf="!amWithinInsightsModal"
                     >&nbsp; ({{
                        constants.pluralise(
                           service.params.CompetitorSummary.CompetitorVehicleCount,
                           "advert",
                           "adverts"
                        )
                     }}
                     )</span
                  >
               </div>
            </div>

            <!-- Retailer Sites Choice -->
            <div *ngIf="!!globalParamsService.getGlobalParam(GlobalParamKeys.UseUserPostcode)" class="mb-2">
                <span class="plate-label">Site</span>
               <div ngbDropdown class="d-inline-block">
                  <button class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
                     <span>{{ selectedRetailerSite?.Name }}</span>
                  </button>

                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                     <ng-container *ngFor="let retailerSite of constants.RetailerSites">
                        <button ngbDropdownItem (click)="selectRetailerSite(retailerSite)">
                           {{ retailerSite.Name }}
                        </button>
                     </ng-container>
                  </div>
               </div>
            </div>

            <!-- Facets -->
            <div *ngFor="let facetFilter of facetFilters" class="checkbox-container">
               <div class="checkbox-header">{{ facetFilter.Facet }}</div>
               <div class="checkbox-list">
                  <div *ngFor="let choice of facetFilter.Choices">
                     <sliderSwitch
                        [rightText]="choice"
                        [styleClass]="'ps-0 pe-0 btn-alt no-label-wrap'"
                        [defaultValue]="selectedFacetChoices[facetFilter.Facet]?.includes(choice)"
                        [blackFont]="true"
                        (toggle)="selectFacet(facetFilter.Facet, choice)"
                     >
                     </sliderSwitch>
                  </div>
               </div>
            </div>
         </div>
         <div>
            <div class="pt-3">
               <button class="btn btn-success" [disabled]="!choiceMade" (click)="getAutoPriceCompetitorAnalysis()">
                  Search
               </button>
            </div>
         </div>
      </div>

      <div class="flex-grow-1" style="height: 100%; display: flex; flex-direction: column">
         <span *ngIf="!service.params.CompetitorSummary">No competitor data to display</span>
         <!-- Table view -->
         <ng-container *ngIf="service.params.CompetitorSummary">
            <ng-container *ngIf="showBlobsAndTableTogether()">
               <!-- Table -->
               <competitorAnalysisTable [filters]="tableFilters"></competitorAnalysisTable>

               <!-- Blob type selector -->
               <div class="buttonGroup mx-0 my-2">
                  <button
                     *ngFor="let field of chartXYFields"
                     class="btn btn-primary"
                     [ngClass]="{ active: showActiveChartButton(field) }"
                     (click)="showChartView(field)"
                  >
                     <i class="fas fa-chart-scatter"></i>
                     <span>&nbsp;{{ field.xPretty + " and " + field.yPretty }}</span>
                  </button>
               </div>

               <div id="blobChartWrapper">
                  <!-- Blobs -->
                  <competitorAnalysisChart  [selectedXYFields]="chartData"></competitorAnalysisChart>
               </div>
            </ng-container>

            <ng-container *ngIf="!showBlobsAndTableTogether()">
               <!-- More compact view, either table OR blobs -->

               <competitorAnalysisTable [isFullScreen]="!inModal" *ngIf="!showBlobs" [filters]="tableFilters"></competitorAnalysisTable>

               <competitorAnalysisChart [selectedXYFields]="chartData" *ngIf="showBlobs"></competitorAnalysisChart>
            </ng-container>
         </ng-container>
      </div>
   </div>
</div>

<ng-template #modalRef let-modal>
   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
         <ng-container *ngIf="autoPriceInsightsService.modalItem.AdvertDetail?.isTradePricing">Retail</ng-container>
         Competitor Analysis ({{
            constants.pluralise(autoPriceInsightsService.competitorListCount, "advert", "adverts")
         }})
      </h4>

      <button type="button" class="close" aria-label="Close" (click)="closeModal()">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>

   <div class="modal-body" [ngClass]="constants.environment.customer">
      <competitorAnalysis [inModal]="true" ></competitorAnalysis>
   </div>

   <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="closeModal()">Close</button>
   </div>
</ng-template>
