﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
    public class PublicHoliday
    {
        [Key]
        public int Id { get; set; }
        public DateTime Date { get; set; }

        public int DealerGroup_Id { get; set; }
        [ForeignKey("DealerGroup_Id")]
        public DealerGroup DealerGroup { get; set; }

        public ICollection<PublicHolidayRegionMap> RegionMaps { get; set; }
    }
}

