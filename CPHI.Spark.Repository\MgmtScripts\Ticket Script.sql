--FOR SPK-4996 build loader for emg stock report
INSERT INTO VehicleTypes (Code, Description, DescriptionFull, ShowQualifyingStatus, IsQualifying, IsGroupQualifying, Type, SuperType, DealerGroup_Id)
VALUES
('R','Retail', 'Retail Vehicle', 1, 1, 0, 'Retail','Retail',22);

-- SPK-5014 Populate PublicHolidayRegions
INSERT INTO PublicHolidayRegions
VALUES
('England'), -- 1
('Wales'), -- 2
('Scotland'), -- 3
('Northern Ireland'), -- 4
('Spain'), -- 5
('USA'); -- 6

-- Insert some sample data, Christmas day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT ph.Id, 1
FROM PublicHolidays ph
WHERE DAY(ph.Date) = 25  
AND MONTH(ph.Date) = 12; 

INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT ph.Id, 2
FROM PublicHolidays ph
WHERE DAY(ph.Date) = 25  
AND MONTH(ph.Date) = 12; 

INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT ph.Id, 3
FROM PublicHolidays ph
WHERE DAY(ph.Date) = 25  
AND MONTH(ph.Date) = 12; 

INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT ph.Id, 4
FROM PublicHolidays ph
WHERE DAY(ph.Date) = 25  
AND MONTH(ph.Date) = 12; 

-- Update RegionIds (for Renault / only two active sites are in Wales)
UPDATE
Sites
SET PublicHolidayRegionId = 1
WHERE Id NOT IN (22,23)
AND IsActive = 1

UPDATE
Sites
SET PublicHolidayRegionId = 2
WHERE Id IN (22,23)
AND IsActive = 1
--- SPK-5405 Populate AutoPriceSparkTableStates (Brindley)
INSERT INTO [autoprice].[AutoPriceSparkTableStates] 
    ([AutoPriceTableStateId], [SetById], [DealerGroupId], [SortIndex])
VALUES 
    (187, 1101, 9, 1),
    (188, 1101, 9, 2),
    (189, 1101, 9, 3),
    (190, 1101, 9, 4),
    (191, 1101, 9, 5),
    (192, 1101, 9, 6),
    (193, 1101, 9, 7),
    (194, 1101, 9, 8),
    (195, 1101, 9, 9),
    (196, 1101, 9, 10),
    (197, 1101, 9, 11);


-- SPK-5391 Amend weightings for AutoNow and Three10

--SPK-4103 Enable stock table columns for Vindis
UPDATE [autoprice].RetailerSites SET StockReport_ShowDmsSellingPrice = 1, StockReport_ShowVsDmsSellingPrice = 1
  WHERE DealerGroup_Id = 3;







