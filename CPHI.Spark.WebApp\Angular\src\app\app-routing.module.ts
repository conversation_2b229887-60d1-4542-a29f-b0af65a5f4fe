import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppComponent } from './app.component';

import { ChangePasswordComponent } from './components/change-password/change-password.component';
import { ForgotpasswordComponent } from './components/forgotpassword/forgotpassword.component';
import { LoginComponent } from './pages/login/login.component';
import { NoWebAccessComponent } from './pages/login/nowebaccess.component';
import { ManageUserComponent } from './components/manage-user/manage-user.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
//import the components
//other imports
import { LoggedInGuard } from './loggedIn.guard';
import { AftersalesLeagueComponent } from './pages/aftersalesLeague/aftersalesLeague.component';
import { AlcopaSummaryComponent } from './pages/alcopaSummary/alcopaSummary.component';
import { CitNowwComponent } from './pages/citNow/citNow.component';
import { ImageRatiosComponent } from './pages/imageRatios/imageRatios.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { DealsDoneThisWeekComponent } from './pages/dealsDoneThisWeek/dealsDoneThisWeek.component';
import { DealsForTheMonthComponent } from './pages/dealsForTheMonth/dealsForTheMonth.component';
import { DebtsComponent } from './pages/debts/debts.component';
import { EvhcComponent } from './pages/evhc/evhc.component';
import { FAndISummaryComponent } from './pages/fAndISummary/fAndISummary.component';
import { GDPRComponent } from './pages/gdpr/gdpr.component';
import { HandoverDiaryComponent } from './pages/handoverDiary/handoverDiary.component';
import { OrderBookComponent } from './pages/orderBook/orderBook.component';
import { PartsStockComponent } from './pages/partsStock/partsStock.component';
import { PartsSummaryComponent } from './pages/partsSummary/partsSummary.component';
import { PerformanceLeagueComponent } from './pages/performanceLeague/performanceLeague.component';
import { RegistrationsPositionComponent } from './pages/registrationsPosition/registrationsPosition.component';
import { ReportingCentreComponent } from './pages/reportingCentre/reportingCentre.component';
import { SalesActivityComponent } from './pages/salesActivity/salesActivity.component';
import { SalesCommissionComponent } from './pages/salesCommission/salesCommission.component';
import { SalesmanEfficiencyComponent } from './pages/salesmanEfficiency/salesmanEfficiency.component';
import { SalesPerformanceComponent } from './pages/salesPerformance/salesPerformance.component';
import { ScratchCardComponent } from './pages/scratchCard/scratchCard.component';
import { ServiceBookingsComponent } from './pages/serviceBookings/serviceBookings.component';
import { ServiceSummaryComponent } from './pages/serviceSummary/serviceSummary.component';
import { StockLandingComponent } from './pages/stockLanding/stockLanding.component';
import { StockListComponent } from './pages/stockList/stockList.component';
import { StockReportComponent } from './pages/stockReport/stockReport.component';
import { SuperCupComponent } from './pages/superCup/superCup.component';
import { SuperCupTwoComponent } from './pages/superCupTwo/superCupTwo.component';
import { UserSetupComponent } from './pages/userSetup/userSetup.component';
import { VocComponent } from './pages/voc/voc.component';
import { WhiteboardComponent } from './pages/whiteboard/whiteboard.component';
import { SalesExecReviewComponent } from './pages/salesExecReview/salesExecReview.component';
import { DistrinetComponent } from './pages/distrinet/distrinet.component';
import { LiveForecastInputComponent } from './pages/liveForecastInput/liveForecastInput.component';
import { LiveForecastStatusComponent } from './pages/liveForecastStatus/liveForecastStatus.component';
import { LiveForecastReviewComponent } from './pages/liveForecastReview/liveForecastReview.component';
import { UpsellsComponent } from './pages/upsells/upsells.component';
import { PerformanceTrendsComponent } from './pages/performanceTrends/performanceTrends.component';
import { FleetOrderbookComponent } from './pages/fleetOrderbook/fleetOrderbook.component';
import { StockInsightComponent } from './pages/autoprice/stockInsight/stockInsight.component';
import { PricingDashboardComponent } from './pages/autoprice/pricingDashboard/pricingDashboard.component';
import { AdvertListingDetailComponent } from './pages/autoprice/advertListingDetail/advertListingDetail.component';
import { LocationOptimiserComponent } from './pages/autoprice/locationOptimiser/locationOptimiser.component';
import { LeavingVehiclesComponent } from './pages/autoprice/leavingVehicles/leavingVehicles.component';
//import { ApplyStrategyComponent } from './pages/autoprice/applyStrategy/applyStrategy.component';
import { VehicleValuationComponent } from './pages/autoprice/vehicleValuation/vehicleValuation.component';
import { CommissionGuard } from './commission.guard';
import { StockLandingGuard } from './stockLanding.guard';
import { LocalBargainsComponent } from './pages/autoprice/localBargains/localBargains.component';
import { BulkUploadComponent } from './pages/autoprice/bulkUpload/bulkUpload.component';
import { HomeComponent } from './pages/autoprice/home/<USER>';
import { OptOutsComponent } from './pages/autoprice/optOuts/optOuts.component';
import { TodayPricesComponent } from './pages/autoprice/todayPrices/todayPrices.component';
import { SiteSettingsComponent } from './pages/autoprice/siteSettings/siteSettings.component';
import { TeleStatsComponent } from './pages/teleStats/teleStats.component';
import { LeavingVehicleTrendsComponent } from './pages/autoprice/leavingVehicleTrends/leavingVehicleTrends.component';
import { LeavingVehicleDetailComponent } from './pages/autoprice/leavingVehicleDetail/leavingVehicleDetail.component';
import { AdvertSimpleListingComponent } from './pages/autoprice/advertSimpleListing/advertSimpleListing.component';
import { SalesIncentiveComponent } from './pages/salesIncentive/salesIncentive.component';
import { SimpleExamplePageComponent } from './pages/simpleExamplePage/simpleExamplePage.component';
import { OrderBookNewComponent } from './pages/orderBookNew/orderBookNew.component';
import { StatsDashboardComponent } from './pages/autoprice/statsDashboard/statsDashboard.component';
import { StockProfilerComponent } from './pages/autoprice/stockProfiler/stockProfiler.component';
import { SitesLeagueComponent } from './pages/autoprice/sitesLeague/sitesLeague.component';
import { UsageReportComponent } from './pages/usageReport/usagereport.component';
import { LeavingVehicleTrendsOverTimeComponent } from './pages/autoprice/leavingVehicleTrendsOverTime/leavingVehicleTrendsOverTime.component';
import { PublicHolidaysComponent } from './pages/publicHolidays/publicHolidays.component';

const routes: Routes = [
  //{path:'',component:DashboardVindisComponent, canActivate:[LoggedInGuard],},
  //{ path: 'home', component: DashboardVindisComponent,canActivate:[LoggedInGuard],},  //
    
  { path: '/', component: AppComponent, canActivate:[LoggedInGuard],},
  { path: 'dashboard', component: DashboardComponent, canActivate:[LoggedInGuard],},
  { path: 'dealsDoneThisWeek', component: DealsDoneThisWeekComponent, canActivate:[LoggedInGuard], },
  { path: 'dealsForTheMonth', component: DealsForTheMonthComponent, canActivate:[LoggedInGuard], },
  { path: 'fAndISummary', component: FAndISummaryComponent, canActivate:[LoggedInGuard], },
  { path: 'handoverDiary', component: HandoverDiaryComponent, canActivate:[LoggedInGuard], },
  { path: 'orderBook', component: OrderBookComponent, canActivate:[LoggedInGuard], },
  { path: 'orderBookNew', component: OrderBookNewComponent, canActivate:[LoggedInGuard], },
  { path: 'fleetOrderbook', component: FleetOrderbookComponent, canActivate:[LoggedInGuard], },
  { path: 'teleStats', component: TeleStatsComponent, canActivate:[LoggedInGuard], },

  { path: 'partsSummary', component: PartsSummaryComponent, canActivate:[LoggedInGuard], },
  { path: 'performanceLeague', component: PerformanceLeagueComponent, canActivate:[LoggedInGuard], },
  { path: 'performanceTrends', component: PerformanceTrendsComponent, canActivate:[LoggedInGuard], },
  { path: 'aftersalesLeague', component: AftersalesLeagueComponent, canActivate:[LoggedInGuard], },
  { path: 'salesmanEfficiency', component: SalesmanEfficiencyComponent, canActivate:[LoggedInGuard], },
  { path: 'registrationsPosition', component: RegistrationsPositionComponent, canActivate:[LoggedInGuard], },
  { path: 'reportingCentre', component: ReportingCentreComponent, canActivate:[LoggedInGuard], },
  { path: 'serviceSummary', component: ServiceSummaryComponent, canActivate:[LoggedInGuard], },
  { path: 'stockList', component: StockListComponent, canActivate:[LoggedInGuard], },
  { path: 'stockReport', component: StockReportComponent, canActivate:[LoggedInGuard], },
  { path: 'whiteboard', component: WhiteboardComponent, canActivate:[LoggedInGuard], },
  { path: 'debts', component: DebtsComponent,  canActivate:[LoggedInGuard], },
  { path: 'salesPerformance', component: SalesPerformanceComponent,  canActivate:[LoggedInGuard], },
  { path: 'salesCommission', component: SalesCommissionComponent,  canActivate:[CommissionGuard,LoggedInGuard], },
  
  { path: 'superCup', component: SuperCupComponent,  canActivate:[LoggedInGuard], },
  { path: 'superCupTwo', component: SuperCupTwoComponent,  canActivate:[LoggedInGuard], },
  { path: 'voc', component: VocComponent,  canActivate:[LoggedInGuard], },
  { path: 'citnow', component: CitNowwComponent,  canActivate:[LoggedInGuard], },
  { path: 'imageRatios', component: ImageRatiosComponent,  canActivate:[LoggedInGuard], },
  { path: 'evhc', component: EvhcComponent,  canActivate:[LoggedInGuard], },
  { path: 'partsStock', component: PartsStockComponent,  canActivate:[LoggedInGuard], },
  { path: 'serviceBookings', component: ServiceBookingsComponent,  canActivate:[LoggedInGuard], },
  { path: 'stockLanding', component: StockLandingComponent,  canActivate:[StockLandingGuard,LoggedInGuard], },
  { path: 'scratchCard', component: ScratchCardComponent, canActivate: [LoggedInGuard], },
  { path: 'alcopa', component: AlcopaSummaryComponent, canActivate: [LoggedInGuard], },
  { path: 'login', component: LoginComponent },
  { path: 'noWebAccess', component: NoWebAccessComponent },
  { path: 'forgotpassword', component: ForgotpasswordComponent },
  { path: 'resetpassword', component: ResetPasswordComponent },
  { path: 'changePassword', component: ChangePasswordComponent, canActivate:[LoggedInGuard] },
  { path: 'manage', component: ManageUserComponent, canActivate:[LoggedInGuard] },
  { path: 'userMaintenance', component: UserSetupComponent, canActivate:[LoggedInGuard], },
  { path: 'salesActivity', component: SalesActivityComponent, canActivate: [LoggedInGuard] },
  { path: 'gdpr', component: GDPRComponent, canActivate: [LoggedInGuard] },
  { path: 'salesExecReview', component: SalesExecReviewComponent, canActivate: [LoggedInGuard] },
  { path: 'distrinet', component: DistrinetComponent, canActivate: [LoggedInGuard] },
  { path: 'liveForecastInput', component: LiveForecastInputComponent, canActivate: [LoggedInGuard] },
  { path: 'liveForecastStatus', component: LiveForecastStatusComponent, canActivate: [LoggedInGuard] },
  { path: 'liveForecastReview', component: LiveForecastReviewComponent, canActivate: [LoggedInGuard] },
  { path: 'upsells', component: UpsellsComponent, canActivate: [LoggedInGuard] },
  
  
  
  
  { path: 'home', component: HomeComponent, canActivate: [LoggedInGuard] },    //home     | boolean is: pricingHome
  { path: 'stockDashboard', component: StockInsightComponent, canActivate: [LoggedInGuard] },  //blobs thing for current stock   | boolean is: stockInsight
  { path: 'summaryDashboard', component: PricingDashboardComponent, canActivate: [LoggedInGuard] }, //top level dashboard   | boolean is: summaryDashboard
  //{ path: 'strategyBuilder', component: StrategyBuilderComponent, canActivate: [LoggedInGuard] },  //where build strategy   | boolean is: strategyBuilder
  //{ path: 'applyStrategy', component: ApplyStrategyComponent, canActivate: [LoggedInGuard] },  //DEAD NOW   | boolean is: applyStrategy
  { path: 'stockReports', component: AdvertListingDetailComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: stockReports
  { path: 'reg/:regNumber', component: AdvertListingDetailComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: stockReports
  { path: 'advertSimpleListing', component: AdvertSimpleListingComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: stockReports
  { path: 'locationOptimiser', component: LocationOptimiserComponent, canActivate: [LoggedInGuard] },  //locationOptimiser   | boolean is: locationOptimiser
  { path: 'soldDashboard', component: LeavingVehiclesComponent, canActivate: [LoggedInGuard] }, //leaving vehicles   | boolean is: leavingVehicles
  { path: 'vehicleValuation', component: VehicleValuationComponent, canActivate: [LoggedInGuard] },  //DEAD NOW   | boolean is: vehicleValuation
  { path: 'bulkValuation', component: BulkUploadComponent, canActivate: [LoggedInGuard] }, //  | boolean is: bulkValuation
  { path: 'valuation/:id/:reg/:mileage/:condition', component: BulkUploadComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: stockReports
  { path: 'localBargains', component: LocalBargainsComponent, canActivate: [LoggedInGuard] }, //local bargains   | boolean is: localBargains
  { path: 'optOuts', component: OptOutsComponent, canActivate: [LoggedInGuard] },  //optOuts in place   | boolean is: optOuts
  { path: 'todaysPrices', component: TodayPricesComponent, canActivate: [LoggedInGuard] },  //todayPriceChanges   | boolean is: todayPriceChanges
  { path: 'siteSettings', component: SiteSettingsComponent, canActivate: [LoggedInGuard] },  //siteSettings   | boolean is: autoPriceSiteSettings

  { path: 'leavingVehicleTrends', component: LeavingVehicleTrendsComponent, canActivate: [LoggedInGuard] },
  { path: 'leavingVehicleDetail', component: LeavingVehicleDetailComponent, canActivate: [LoggedInGuard] },
  { path: 'leavingVehicleTrendsOverTime', component: LeavingVehicleTrendsOverTimeComponent, canActivate: [LoggedInGuard] },

  { path: 'salesIncentive', component: SalesIncentiveComponent, canActivate: [LoggedInGuard] },
  { path: 'simpleExample', component: SimpleExamplePageComponent, canActivate: [LoggedInGuard] },
  { path: 'statsDashboard', component: StatsDashboardComponent, canActivate: [LoggedInGuard] },
  { path: 'sitesLeague', component: SitesLeagueComponent, canActivate: [LoggedInGuard] },
  { path: 'stockProfiler', component: StockProfilerComponent, canActivate: [LoggedInGuard] },
  { path: 'usageReport', component: UsageReportComponent, canActivate: [LoggedInGuard] },
  { path: 'publicHolidays', component: PublicHolidaysComponent, canActivate: [LoggedInGuard] },
  
  
  //catch all
  {path:'**',redirectTo:'/'},
 

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { 
  

}



