<!-- The menu -->
<div ngbDropdown class="userMenuDropdown" container="body">
    <button type="button" id="accountDropdownToggle" ngbDropdownToggle>
        <img id="userProfilePic" [src]="getProfilePictureURL()" (error)="setDefaultProfilePicture()">
    </button>
    <div ngbDropdownMenu>
        <span  disabled ngbDropdownItem>{{ selectionsService.user.Name }}</span>
        <span disabled ngbDropdownItem>{{ selectionsService.user.RoleName }}</span>
        <span disabled ngbDropdownItem>{{ selectionsService.user.Email }}</span>
        <div class="dropdown-divider"></div>
        <span disabled ngbDropdownItem>Choose Theme:</span>
        <div class="d-flex">
            <button class="btn btn-primary" [ngClass]="{ 'active': constantsService.savedTheme === 'grey' || !constantsService.savedTheme}"
                (click)="chooseTheme('grey')">
                Grey
            </button>
            <button class="btn btn-primary" [ngClass]="{ 'active': constantsService.savedTheme === 'green' }"
                (click)="chooseTheme('green')">
                Green
            </button>
        </div>
        <div class="dropdown-divider"></div>
        <button ngbDropdownItem (click)="openProfilePicModal()">Update Profile Picture</button>
        <button ngbDropdownItem *ngIf="showPublicHolidays" (click)="goToPublicHolidays()">Public Holidays</button>
        <button ngbDropdownItem *ngIf="showSwitchDG" (click)="switchDealerGroup()">Change group ({{selectionsService.dealerGroupName}})</button>
        <!-- <button ngbDropdownItem *ngIf="showSwitchDG" (click)="goToSimpleExamplePage()">Go to simple example page</button> -->
        <div class="dropdown-divider"></div>
        <button ngbDropdownItem (click)="changePassword()">Change Password</button>
        <button ngbDropdownItem (click)="logout()">Logout</button>
        
    </div>
</div>

<!-- The profile pic stuff -->
<canvas #resizeMainImageCanvas id="resizeMainImageCanvas"></canvas>

<ng-template #cropperModal let-modal>

    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            {{ constantsService.translatedText.UpdateProfilePicture }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div [ngClass]="constantsService.environment.customer" class="modal-body lowHeight">
        <div *ngIf="!imageChangedEvent" id="chooseImage">
            <input id="chooseFilesButton" type="file" (change)="fileChangeEvent($event)" multiple="false" />
        </div>

        <div id="cropperHolder">
            <table *ngIf="imageChangedEvent">
                <tr>
                    <td>Select area to use for profile picture</td>
                    <td>Profile picture preview</td>
                <tr>
                    <td>
                        <div id="sourceImageContainer">
                            <button id="closeImageButton" class="btn btn-danger"
                                (click)="imageChangedEvent=null">x</button>
                            <image-cropper [imageChangedEvent]="imageChangedEvent" [maintainAspectRatio]="true"
                                [resizeToWidth]="100" format="png" [roundCropper]="true"
                                (imageCropped)="imageCropped($event)"></image-cropper>

                        </div>
                    </td>
                    <td>
                        <div id="resultAndButton">
                            <img [src]="croppedImage" />
                            <button *ngIf="!status" class="btn btn-primary" (click)="setProfilePic()">Set Profile
                                Picture</button>
                            <button *ngIf="status=='failed'" class="btn btn-danger">Failed To Save</button>
                            <button *ngIf="status=='success'" class="btn btn-success">Profile Picture Updated</button>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
</ng-template>