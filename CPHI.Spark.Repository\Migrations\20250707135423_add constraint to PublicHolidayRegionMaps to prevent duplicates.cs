﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class addconstrainttoPublicHolidayRegionMapstopreventduplicates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayId",
                table: "PublicHolidayRegionMaps");

            migrationBuilder.AddColumn<string>(
                name: "BodyType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Doors",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FuelType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransmissionType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Trim",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayId_PublicHolidayRegionId",
                table: "PublicHolidayRegionMaps",
                columns: new[] { "PublicHolidayId", "PublicHolidayRegionId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayId_PublicHolidayRegionId",
                table: "PublicHolidayRegionMaps");

            migrationBuilder.DropColumn(
                name: "BodyType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis");

            migrationBuilder.DropColumn(
                name: "Doors",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis");

            migrationBuilder.DropColumn(
                name: "FuelType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis");

            migrationBuilder.DropColumn(
                name: "TransmissionType",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis");

            migrationBuilder.DropColumn(
                name: "Trim",
                schema: "autoprice",
                table: "VehicleValuationCompetitorAnalysis");

            migrationBuilder.CreateIndex(
                name: "IX_PublicHolidayRegionMaps_PublicHolidayId",
                table: "PublicHolidayRegionMaps",
                column: "PublicHolidayId");
        }
    }
}
