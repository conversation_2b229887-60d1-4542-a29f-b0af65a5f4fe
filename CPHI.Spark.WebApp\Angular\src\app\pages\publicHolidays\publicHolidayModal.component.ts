import { Component, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { PublicHolidayRequest, PublicHolidayViewModel, PublicHolidayRegion } from 'src/app/model/publicHoliday.model';

@Component({
  selector: 'publicHolidayModal',
  template: `
    <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title">
          {{ isEditMode ? 'Edit Public Holiday' : 'Add Public Holiday' }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      
      <div [ngClass]="constants.environment.customer" class="modal-body">
        <form [formGroup]="holidayForm" (ngSubmit)="saveHoliday(modal)">
          
          <div class="form-group mb-3">
            <label for="date" class="form-label">Date *</label>
            <input 
              type="date" 
              id="date" 
              class="form-control" 
              formControlName="date"
              [class.is-invalid]="holidayForm.get('date')?.invalid && holidayForm.get('date')?.touched">
            <div class="invalid-feedback" *ngIf="holidayForm.get('date')?.invalid && holidayForm.get('date')?.touched">
              Date is required
            </div>
          </div>

          <div class="form-group mb-3">
            <label class="form-label">Regions *</label>
            <div class="region-checkboxes">
              <div class="form-check" *ngFor="let region of availableRegions">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  [id]="'region-' + region.id"
                  [value]="region.id"
                  (change)="onRegionChange($event, region.id)">
                <label class="form-check-label" [for]="'region-' + region.id">
                  {{ region.region }}
                </label>
              </div>
            </div>
            <div class="text-danger" *ngIf="selectedRegions.length === 0 && formSubmitted">
              At least one region must be selected
            </div>
          </div>

        </form>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Cancel')">
          Cancel
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          (click)="saveHoliday(modal)"
          [disabled]="!isFormValid()">
          {{ isEditMode ? 'Update' : 'Add' }} Holiday
        </button>
      </div>
    </ng-template>
  `,
  styles: [`
    .region-checkboxes {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 4px;
    }
    
    .form-check {
      margin-bottom: 8px;
    }
    
    .modal-body {
      max-height: 70vh;
      overflow-y: auto;
    }
  `]
})
export class PublicHolidayModalComponent {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  @Output() holidaySaved = new EventEmitter<void>();

  private destroy$ = new Subject<void>();
  
  holidayForm: FormGroup;
  isEditMode = false;
  currentHoliday: PublicHolidayViewModel | null = null;
  availableRegions: PublicHolidayRegion[] = [];
  selectedRegions: number[] = [];
  formSubmitted = false;

  constructor(
    public constants: ConstantsService,
    private modalService: NgbModal,
    private formBuilder: FormBuilder,
    private getDataMethodsService: GetDataMethodsService
  ) {
    this.initializeForm();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.holidayForm = this.formBuilder.group({
      date: ['', Validators.required]
    });
  }

  openModal(holiday: PublicHolidayViewModel | null, regions: PublicHolidayRegion[]): void {
    this.currentHoliday = holiday;
    this.isEditMode = holiday !== null;
    this.availableRegions = regions;
    this.selectedRegions = [];
    this.formSubmitted = false;

    if (this.isEditMode && holiday) {
      // Populate form with existing data
      this.holidayForm.patchValue({
        date: this.formatDateForInput(holiday.date)
      });
      this.selectedRegions = [...holiday.regionIds];
      
      // Set checkboxes
      setTimeout(() => {
        holiday.regionIds.forEach(regionId => {
          const checkbox = document.getElementById(`region-${regionId}`) as HTMLInputElement;
          if (checkbox) {
            checkbox.checked = true;
          }
        });
      }, 100);
    } else {
      // Reset form for new holiday
      this.holidayForm.reset({
        date: '',
      });
    }

    this.modalService.open(this.modalRef, { 
      windowClass: "publicHolidayModal", 
      size: 'lg', 
      keyboard: false, 
      ariaLabelledBy: 'modal-basic-title' 
    }).result.then((result) => {
      // Modal closed with save
    }, (reason) => {
      // Modal dismissed
      this.resetForm();
    });
  }

  private formatDateForInput(date: Date): string {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onRegionChange(event: any, regionId: number): void {
    if (event.target.checked) {
      if (!this.selectedRegions.includes(regionId)) {
        this.selectedRegions.push(regionId);
      }
    } else {
      this.selectedRegions = this.selectedRegions.filter(id => id !== regionId);
    }
  }

  isFormValid(): boolean {
    return this.holidayForm.valid && this.selectedRegions.length > 0;
  }

  saveHoliday(modal: any): void {
    this.formSubmitted = true;
    
    if (!this.isFormValid()) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.holidayForm.controls).forEach(key => {
        this.holidayForm.get(key)?.markAsTouched();
      });
      return;
    }

    const formValue = this.holidayForm.value;
    const request: PublicHolidayRequest = {
      date: new Date(formValue.date),
      regionIds: this.selectedRegions
    };

    const operation = this.isEditMode && this.currentHoliday
      ? this.getDataMethodsService.updatePublicHoliday(this.currentHoliday.id, request)
      : this.getDataMethodsService.addPublicHoliday(request);

    operation
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response.Success) {
            this.holidaySaved.emit();
            modal.close('Save');
            this.resetForm();
          } else {
            alert('Error saving holiday: ' + response.Message);
          }
        },
        error: (error) => {
          console.error('Error saving holiday:', error);
          alert('Error saving holiday');
        }
      });
  }

  private resetForm(): void {
    this.holidayForm.reset();
    this.selectedRegions = [];
    this.currentHoliday = null;
    this.isEditMode = false;
    this.formSubmitted = false;
  }
}
