﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Linq;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IPriceChangesDataAccess
   {
      Task SaveManualPriceChange(PriceChangeManualItem itemToSave);
      Task SavePriceChangeItems(ConcurrentBag<PriceChangeAutoItem> items);
      Task UpdatePriceChangeAutoItemsDaysToSellAndPriceIndicator(List<PricingChange> updates);
      Task<IEnumerable<PriceChangeAutoItemsVM>> GetAutoPriceChangesFromTodayWhichHaveAPrice(DealerGroupName dealerGroup);
      Task CreateNewRatingsForUpdatedPrices(IEnumerable<PricingChangeNew> okChanges);
      Task SetPriceChanges(List<int> priceChangeIds, int userDealerGroupId, int userId);
      Task<PriceChangeToday> GetTodayAutoPriceChangeForAd(int advertId);
      Task<IEnumerable<PricingChangeNew>> GetAutoPriceChanges(GetPriceChangesNewParams parms);
      Task<List<PriceChangeAutoItemWithDates>> GetTodayAutoPriceChanges(DateTime chosenDate, DealerGroupName dealerGroup);
      Task SaveAutoPriceChanges(List<PriceChangeAutoItem> changes);
      Task UpdatePriceChangeAutoItems(List<PricingChangeMinimal> updates, DateTime dateSent);
      Task<IEnumerable<PricingChangeMinimal>> GetManualPriceChanges(string retailerSiteIds, int dealerGroupId);
      Task UpdatePriceChangeManualItems(List<PricingChangeMinimal> updates, DateTime dateSent);
   }

   public class PriceChangesDataAccess : IPriceChangesDataAccess
   {
      private readonly string _connectionString;
      public PriceChangesDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }



      public async Task MarkPricesAsHavingBeenEmailed(List<PricingChangeMinimal> priceChanges)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var priceChangeIds = priceChanges.Select(pc => pc.PriceChangeId).ToList();

            // Update all matching records in a single query
            await db.PriceChangeAutoItems
                .Where(x => priceChangeIds.Contains(x.Id))
                .ForEachAsync(priceChange => priceChange.ApproverHasBeenEmailed = true);

            await db.SaveChangesAsync();
         }
      }


      public async Task<PriceChangeToday> GetTodayAutoPriceChangeForAd(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var priceChange = db.PriceChangeAutoItems
                .Where(x => x.VehicleAdvertSnapshot.SnapshotDate.Date == DateTime.Now.Date)
                .Where(x => x.CouldGenerateNewPrice)
                .Include(x => x.VehicleAdvertSnapshot).ThenInclude(x => x.VehicleAdvert).ThenInclude(x => x.RetailerSite)
                .FirstOrDefault(x => x.VehicleAdvertSnapshot.VehicleAdvert_Id == advertId);


            //now find if it has been opted out on day
            if (priceChange != null)
            {
               var optOutOnDay = db.VehicleOptOuts.FirstOrDefault(x => x.CreatedDate > priceChange.CreatedDate && x.ActualEndDate > priceChange.CreatedDate && x.VehicleAdvert_Id == priceChange.VehicleAdvertSnapshot.VehicleAdvert_Id);
               bool isOptedOutOnDay = optOutOnDay != null;
               return new PriceChangeToday(priceChange, isOptedOutOnDay);
            }
            else
            {
               return null;
            }
         }
      }

      public async Task<IEnumerable<PricingChangeMinimal>> GetManualPriceChanges(string retailerSiteIds, int dealerGroupId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("dealerGroupId", dealerGroupId);
            paramList.Add("retailerSiteIds", retailerSiteIds);
            var priceChanges = await dapper.GetAllAsync<PricingChangeMinimal>("autoprice.GET_ManualPriceChanges", paramList, commandType: System.Data.CommandType.StoredProcedure, null, 180);

            return priceChanges;
         }
      }


      public async Task<IEnumerable<PricingChangeNew>> GetAutoPriceChanges(GetPriceChangesNewParams parms)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("retailerSiteIds", parms.RetailerSiteIds);
            paramList.Add("includeNewVehicles", parms.IncludeNewVehicles);
            paramList.Add("includeLCVs", parms.IncludeLCVs);
            paramList.Add("includeSmallChanges", parms.IncludeSmallChanges);
            paramList.Add("includeUnPublishedAds", parms.IncludeUnPublishedAds);
            paramList.Add("includeUnPublishedAdBasedOnSiteSetting", parms.IncludeUnPublishedAdBasedOnSiteSetting);
            paramList.Add("includeNewVehiclesBasedOnSiteSetting", parms.IncludeNewVehiclesBasedOnSiteSetting);
            paramList.Add("includeLifecycleStatusesBasedOnSiteSetting", parms.IncludeLifecycleStatusesBasedOnSiteSetting);
            paramList.Add("filterDaysInStockBasedOnSiteSetting", parms.FilterDaysInStockBasedOnSiteSetting);
            paramList.Add("includeVehicleTypesBasedOnSiteSetting", parms.IncludeVehicleTypesBasedOnSiteSetting);
            paramList.Add("OnlyKeyChanges", parms.OnlyKeyChanges);
            paramList.Add("dealerGroupId", parms.DealerGroupId);
            paramList.Add("lifecycleStatuses", parms.LifeCycleStatuses != null ? string.Join(",", parms.LifeCycleStatuses) : null);

            if (parms.ChosenDate != null)
            {
               paramList.Add("chosenDate", parms.ChosenDate);
            }


            var autoPriceChanges = await dapper.GetAllAsync<PricingChangeNew>("autoprice.GET_AutoPriceChangesNew", paramList, commandType: System.Data.CommandType.StoredProcedure, null, 180);

            //now workout what message to put against each price change e.g. 'Will be actioned at 6pm'
            using (var db = new CPHIDbContext(_connectionString))
            {
               var retailerSites = await db.RetailerSites.Where(x => x.DealerGroup_Id == parms.DealerGroupId).ToListAsync();

               bool todayIsBankHoliday = (await db.PublicHolidays.FirstOrDefaultAsync(x => x.Date == DateTime.Now.Date)) != null;

               var rsLookup = retailerSites.ToDictionary(x => x.Id);

               var dayOfWeek = (parms.ChosenDate ?? DateTime.Now).DayOfWeek;

               foreach (var item in autoPriceChanges)
               {
                  var retailerSite = rsLookup[item.RetailerSiteId];
                  var autoPricingInPlace = (!todayIsBankHoliday || retailerSite.UpdatePricesPubHolidays) && retailerSite.UpdatePricesAutomatically &&
                          (
                              (retailerSite.UpdatePricesMon && dayOfWeek == DayOfWeek.Monday) ||
                              (retailerSite.UpdatePricesTue && dayOfWeek == DayOfWeek.Tuesday) ||
                              (retailerSite.UpdatePricesWed && dayOfWeek == DayOfWeek.Wednesday) ||
                              (retailerSite.UpdatePricesThu && dayOfWeek == DayOfWeek.Thursday) ||
                              (retailerSite.UpdatePricesFri && dayOfWeek == DayOfWeek.Friday) ||
                              (retailerSite.UpdatePricesSat && dayOfWeek == DayOfWeek.Saturday) ||
                              (retailerSite.UpdatePricesSun && dayOfWeek == DayOfWeek.Sunday)
                          );

                  item.IsSetToAutoUpdatePrice = autoPricingInPlace;
                  string actionTime = AutoPriceHelperService.ConvertTo12HourFormat(retailerSite.WhenToActionChangesEachDay);

                  item.workoutStatus(autoPricingInPlace ? actionTime : null, retailerSite.DealerGroup_Id);

                  item.RetailerAdminFee = retailerSite.AdminFee;

                  if (parms.BandingsDict != null)
                  {
                     item.UpdateVsStrategyBanding(parms.BandingsDict);
                  }


               }


               return autoPriceChanges;
            }

         }
      }


      public async Task SaveAutoPriceChanges(List<PriceChangeAutoItem> changes)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.PriceChangeAutoItems.AddRange(changes);
            await db.SaveChangesAsync();
         }
      }


      public async Task<List<PriceChangeAutoItemWithDates>> GetTodayAutoPriceChanges(DateTime chosenDate, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("chosenDate", chosenDate);
            paramList.Add("dealerGroupId", (int)dealerGroup);

            var results = await dapper.GetAllAsync<PriceChangeAutoItemWithDates>("autoprice.GET_TodayAutoPriceChanges", paramList, commandType: System.Data.CommandType.StoredProcedure);
            return results.ToList();
         }
      }


      public async Task SaveManualPriceChange(PriceChangeManualItem itemToSave)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.PriceChangeManualItems.Add(itemToSave);
            await db.SaveChangesAsync();
         }
      }



      public async Task SavePriceChangeItems(ConcurrentBag<PriceChangeAutoItem> items)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            db.PriceChangeAutoItems.AddRange(items);
            await db.SaveChangesAsync();
         }

      }


      public async Task UpdatePriceChangeAutoItems(List<PricingChangeMinimal> updates, DateTime dateSent)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            foreach (PricingChangeMinimal updatePriceResult in updates)
            {
               var dbItem = db.PriceChangeAutoItems.First(x => x.Id == updatePriceResult.PriceChangeId);
               dbItem.DateSent = dateSent;
               dbItem.DateConfirmed = updatePriceResult.DateConfirmed;
               dbItem.SaveResult = updatePriceResult.DateConfirmed != null ? "OK" : null;

            }
            await db.SaveChangesAsync();
         }
      }    
      
      public async Task UpdatePriceChangeManualItems(List<PricingChangeMinimal> updates, DateTime dateSent)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            foreach (PricingChangeMinimal updatePriceResult in updates)
            {
               var dbItem = db.PriceChangeManualItems.First(x => x.Id == updatePriceResult.PriceChangeId);
               dbItem.DateSent = dateSent;
               dbItem.DateConfirmed = updatePriceResult.DateConfirmed;
               dbItem.SaveResult = updatePriceResult.DateConfirmed != null ? "OK" : null;

            }
            await db.SaveChangesAsync();
         }
      }

      public async Task UpdatePriceChangeAutoItemsDaysToSellAndPriceIndicator(List<PricingChange> updates)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Extract the IDs of the PriceChangeAutoItems from the updates
            var priceChangeIds = updates.Select(update => update.PriceChangeId).ToList();

            // Fetch all relevant PriceChangeAutoItems in one query
            var dbItems = await db.PriceChangeAutoItems
                .Where(x => priceChangeIds.Contains(x.Id))
                .ToListAsync();

            // Create a dictionary for fast lookup of updates by PriceChangeId
            var updatesById = updates.ToDictionary(update => update.PriceChangeId);

            // Update the properties of the fetched PriceChangeAutoItems
            foreach (var dbItem in dbItems)
            {
               if (updatesById.TryGetValue(dbItem.Id, out var updatePriceResult))
               {
                  dbItem.DaysToSell = updatePriceResult.DaysToSell;
                  dbItem.PriceIndicator = updatePriceResult.PriceIndicator;
               }
            }

            // Save all changes to the database
            await db.SaveChangesAsync();
         }
      }

      public async Task CreateNewRatingsForUpdatedPrices(IEnumerable<PricingChangeNew> okChanges)  //PriceChangeId, NowPriceId
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //we have made some changes.  In 15 minutes or so autotrader should be updated.
            //rather than waiting to see these latest prices in the ratings we download tomorrow morning
            //we want to go ahead and make them now
            var changesIds = okChanges.Select(x => x.PriceChangeId).ToList();
            var changesWithRatings = await db.PriceChangeAutoItems.Where(x => changesIds.Contains(x.Id))
                                            .Include(x => x.VehicleAdvertSnapshot)
                                            .ToListAsync();
            //var existingRatings = changesWithRatings.Select(x => x.VehicleWebsiteRating);
            List<VehicleAdvertSnapshot> newAdvertSnaphots = new List<VehicleAdvertSnapshot>();
            foreach (var change in okChanges)
            {
               var existingRating = changesWithRatings.First(x => x.Id == change.PriceChangeId).VehicleAdvertSnapshot;
               newAdvertSnaphots.Add(new VehicleAdvertSnapshot(existingRating, change.NewPrice));
            }

            db.VehicleAdvertSnapshots.AddRange(newAdvertSnaphots);

            //also we must set the old snapshots to no longer be the latest today snapshot
            foreach (var change in okChanges)
            {
               VehicleAdvertSnapshot existingSnapshot = changesWithRatings.First(x => x.Id == change.PriceChangeId).VehicleAdvertSnapshot;
               existingSnapshot.IsTodayLatestSnapshot = false;
            }

            await db.SaveChangesAsync();
         }
      }

      public async Task<IEnumerable<PriceChangeAutoItemsVM>> GetAutoPriceChangesFromTodayWhichHaveAPrice(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("dealerGroupId", (int)dealerGroup);
            return await dapper.GetAllAsync<PriceChangeAutoItemsVM>("autoprice.GET_AutoPriceChangesFromTodayWhichHaveAPrice", paramList, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      public async Task SetPriceChanges(List<int> priceChangeIds, int userDealerGroupId, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var priceChangesToUpdate = await db.PriceChangeAutoItems.Where(x => priceChangeIds.Contains(x.Id)).ToListAsync();

            foreach (var priceChange in priceChangesToUpdate)
            {
               priceChange.ApprovedDate = DateTime.UtcNow;
               priceChange.ApprovedBy_Id = userId;
            }

            db.PriceChangeAutoItems.UpdateRange(priceChangesToUpdate);
            await db.SaveChangesAsync();
         }
      }
   }
}

