﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using CPHI.Spark.WebApp.DataAccess;
using log4net;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing.Text;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class UpdateWebsitePricesService
   {
      private readonly IHttpClientFactory httpClientFactory;
      public UpdateWebsitePricesService(IHttpClientFactory httpClientFactory)
      {
         this.httpClientFactory = httpClientFactory;
      }

      public async Task UpdatePrices(ILog logger, List<DealerGroupName> dealerGroups)
      {
         HttpClient httpClient = this.httpClientFactory.CreateClient();// new System.Net.Http.HttpClient();
         foreach (DealerGroupName dealerGroup in dealerGroups)
         {
            var logMessage = LoggingService.InitLogMessage();
            try
            {

               string _connectionString = ConfigService.GetConnectionString(dealerGroup);

               PriceChangesDataAccess priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
               VehicleAdvertSnapshotsDataAccess snapshotChangesDataAccess = new VehicleAdvertSnapshotsDataAccess(_connectionString);

               PriceChangesService priceChangesService = new PriceChangesService(_connectionString);
               var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
               var retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);


               //if (retailers.Count > 0)
               //{
                  try
                  {
                     //bool includeUnPublished = retailers.First().IncludeUnPublishedAds; 
                     GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup);
                     var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

                     //Early return if none
                     if (todayChangesFirstPass.totalChanges.Count == 0)
                     {
                        continue;
                     }

                     // There are some changes, so now re-run the vehicle opt-out updater
                     var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
                     await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

                     //have to run again in case we just made some optouts
                     GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

                     logger.Info($"Detected {todayChangesResponse.approvedChangesToAction.Count} price change(s) approved for {dealerGroup}, changing prices..");
                     logger.Info($"Detected {todayChangesResponse.overdueChanges.Count} price change(s) overdue time for {dealerGroup}, changing prices..");



                     //---------------------------------------------------
                     // go ahead and actually update the prices
                     //---------------------------------------------------
                     await UpdatePricesThisDealerGroup(logger, retailers, dealerGroup, todayChangesResponse.totalChanges, priceChangesDataAccess, snapshotChangesDataAccess);


                     //---------------------------------------------------
                     //now we should email the approvers
                     //---------------------------------------------------
                     if (todayChangesResponse.approvedChangesToAction.Count > 0)
                     {
                        var reportUpdatedPricesService = new ReportPriceChangesActionedService();
                        //we have to email the approvers
                        var userDataAccess = new UserDataAccess(_connectionString);
                     List<int> approverIds = todayChangesResponse.approvedChangesToAction.Select(x => (int)x.ApprovedById).ToList();
                     List<Person> users = await userDataAccess.GetUsers(approverIds, dealerGroup);

                     var approvedChangesGrouped = todayChangesResponse.approvedChangesToAction.ToLookup(x => x.ApprovedById);
                        foreach (var grouping in approvedChangesGrouped)
                        {
                           var approver = users.FirstOrDefault(x => x.Id == grouping.Key);
                           await reportUpdatedPricesService.SendEmailsForPerson(grouping.ToList(), approver.Email, approver.Name, logger, dealerGroup);
                        }

                        logger.Info($"Informed {approvedChangesGrouped.Count()} approvers about changed prices");
                        await priceChangesDataAccess.MarkPricesAsHavingBeenEmailed(todayChangesResponse.approvedChangesToAction);
                     }

                     //------------------------------------------------------------------------------------------------------
                     //if we have overdue changes do the full on end of day thing to let them know they have been done.
                     //------------------------------------------------------------------------------------------------------
                     if (todayChangesResponse.overdueChanges.Count > 0)
                     {
                        //find out what sites each person has
                        var autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
                        IEnumerable<RetailerSitesPerPerson> userSites = await autoPriceDataAccess.GetRetailerSitesPerUser((int)dealerGroup);
                        IEnumerable<IGrouping<string, RetailerSitesPerPerson>> groupedByPerson = userSites.GroupBy(x => x.Name);


                        //find out all that have been actioned today
                        var allActionedToday = todayChangesResponse.totalChanges.Where(x => !x.IsOptedOutOnDay &&
                            x.DateConfirmed.HasValue && ((DateTime)(x.DateConfirmed)).Date == DateTime.Today
                            )
                            .ToList();

                        //var overduesBySite = todayChangesResponse.overdueChanges.ToLookup(x => x.RetailerSiteId);
                        var reportUpdatedPricesService = new ReportPriceChangesActionedService();
                        int peopleInformed = 0;
                        foreach (var personGroup in groupedByPerson)
                        {
                           List<RetailerSitesPerPerson> thisPersonRetailerSites = personGroup.ToList();
                           var thisPersonRetailerSiteIds = thisPersonRetailerSites.Select(x => x.RetailerSiteId).ToList();
                           var thisPersonChanges = allActionedToday.Where(x => thisPersonRetailerSiteIds.Contains(x.RetailerSiteId)).ToList();
                           if (thisPersonChanges.Count() > 0)
                           {
                              var personEmail = personGroup.ToList().First().Email;
                              var personName = personGroup.ToList().First().Name;
                              await reportUpdatedPricesService.SendEmailsForPerson(thisPersonChanges.ToList(), personEmail, personName, logger, dealerGroup);
                              peopleInformed++;
                           }
                        }

                        logger.Info($"Informed {peopleInformed} people about changed prices");
                        await priceChangesDataAccess.MarkPricesAsHavingBeenEmailed(todayChangesResponse.overdueChanges);
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "UpdatePrices");
                     }

                     if (todayChangesResponse.approvedChangesToAction.Count > 0 && todayChangesResponse.overdueChanges.Count < 0)
                     {
                        //seems unlikely but if someone had approved all the changes then none will be overdue so must finalise log here
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "UpdatePrices");
                     }


                  }
                  catch (Exception ex)
                  {
                     await EmailerService.SendMailOnError(dealerGroup, "UpdateWebsitePrices - Error", ex);
                     LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                  }
              // }

            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "UpdatePrices");
               LoggingService.AddErrorLogMessage(logMessage, ex.Message);
            }


         }

      }

   







      private async Task UpdatePricesThisDealerGroup(
        ILog logger, List<RetailerSite> retailerSites,
        DealerGroupName dealerGroup,
        List<PricingChangeMinimal> changes,
        PriceChangesDataAccess priceChangesDataAccess,
        VehicleAdvertSnapshotsDataAccess snapshotsDataAccess
        )
      {
         try
         {
            //still here, continue
            //LoggingService.UpdateWebSitePrices_LastReportedAlive = DateTime.Now;

            //---------------------------------
            //Go ahead and change them
            //---------------------------------
            ILookup<string, PricingChangeMinimal> changesBySite = changes.ToLookup(x => x.RetailerName);

            var atStockClient = new AutoTraderStockClient(httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
            var atTokenClient = new AutoTraderApiTokenClient(httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
            string atToken = (await atTokenClient.GetToken()).AccessToken;

            //Loop through sites and update prices
            foreach (var siteGrouping in changesBySite)
            {
               string adSiteName = siteGrouping.Key;


               var thisSiteChanges = siteGrouping.ToList();
               //var retailerSiteId = siteGrouping.First().RetailerSiteId;
               //var retailerSite = retailerSites.First(x => x.Id == retailerSiteId);

               //we have some price changes
               DateTime sendDate = DateTime.Now;
               try
               {
                  var site = retailerSites.First(x => x.Name == adSiteName);

                  //-----------------------------------------
                  // 1. Make the price changes on autotrader
                  //-----------------------------------------
                  //var priceChangeParams = thisSiteChanges.ToList().ConvertAll(x => new UpdatePriceParams(x, retailerSiteId));

                  foreach (PricingChangeMinimal    priceChange in thisSiteChanges)
                  {
                     UpdatePriceParams updateParams = new UpdatePriceParams()
                     {
                        AdvertiserId = site.RetailerId,
                        NewPrice = priceChange.NewPrice,
                        WebsiteStockIdentifier = priceChange.WebsiteStockIdentifier
                     };


                     string result = await atStockClient.UpdatePrice(updateParams, atToken, ConfigService.AutotraderBaseURLForChangingPrices);
                     CultureInfo ukCulture = new CultureInfo("en-GB");

                     if (result != "OK")
                     {
                        logger.Error($"Result {result} when keying {priceChange.VehicleReg} from {(priceChange.WasPrice ?? 0).ToString("C0", ukCulture)} to {priceChange.NewPrice.ToString("C0", ukCulture)} for {adSiteName}");
                     }
                     else
                     {
                        logger.Info($"Result {result} when keying {priceChange.VehicleReg} from {(priceChange.WasPrice ?? 0).ToString("C0", ukCulture)} to {priceChange.NewPrice.ToString("C0", ukCulture)} for {adSiteName}");
                     }

                     priceChange.DateConfirmed = result == "OK" ? DateTime.Now : null;
                  }


                  //--------------------------------------------
                  //2. Update the price change status in our db
                  //--------------------------------------------
                  await priceChangesDataAccess.UpdatePriceChangeAutoItems(thisSiteChanges.Where(x=>x.IsAutoChange).ToList(), sendDate);
                  await priceChangesDataAccess.UpdatePriceChangeManualItems(thisSiteChanges.Where(x=>!x.IsAutoChange).ToList(), sendDate);

                  //--------------------------------------------
                  //3. Generate new autoprice ratings
                  // so that when we now go to the modal we
                  // will see the new price
                  //--------------------------------------------
                  IEnumerable<PricingChangeMinimal> okChanges = thisSiteChanges.Where(x => x.DateConfirmed != null); // x.SaveResult == "OK"
                  var okChangesAutochange = okChanges.Where(x => x.IsAutoChange).ToList();
                  await snapshotsDataAccess.CreateNewRatingsForUpdatedPrices(okChangesAutochange);
               }
               catch (Exception ex)
               {
                  logger.Error($"Error while processing site {adSiteName}.  Error follows..");
                  logger.Error(ex.Message);
                  logger.Error(ex.StackTrace);
               }


            }


            logger.Info($"Updated {changes.Count} price change(s) for DG {dealerGroup}.");

         }
         catch (Exception ex)
         {
            { }
            logger.Error(ex);
            throw new Exception(ex.Message, ex);
         }
      }




   }
}
