import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { Observable } from 'rxjs';
import { AccessToken } from 'src/app/model/main.model';
import { AppStartService } from 'src/app/services/appStart.service';
import { AppThemeService } from 'src/app/services/appTheme.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ProfilePictureService } from 'src/app/services/profilePicture.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DealerGroupSelectionModalComponent } from '../dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { DealerGroupVM } from 'src/app/model/DealerGroup';

@Component({
  selector: 'accountDropdown',
  templateUrl: './accountDropdown.component.html',
  styleUrls: ['./accountDropdown.component.scss']
})
export class AccountDropdownComponent implements OnInit {
  @ViewChild('cropperModal', { static: true }) cropperModal: ElementRef;
  @ViewChild('resizeMainImageCanvas') resizeMainImageCanvas: ElementRef;

  imageChangedEvent: any = '';
  croppedImage: any = '';
  imageElement: ElementRef;
  status: string;

  get showSwitchDG(){
    return this.selectionsService.userDealerGroups.length > 1;
  }

  get showPublicHolidays(){
    return this.selectionsService.user.RoleName === "SysAdministrator";
  }

  constructor(
    public profilePictureService: ProfilePictureService,
    public selectionsService: SelectionsService,
    public constantsService: ConstantsService,
    public appThemeService: AppThemeService,
    public modalService: NgbModal,
    public getDataService: GetDataMethodsService,
    public appStartService: AppStartService,
    public router: Router
  ) { }

  ngOnInit(): void {
  }

  getProfilePictureURL() {
    let url: string = this.profilePictureService.getProfilePictureURL(this.selectionsService.user.PersonId);
    return url += `&${this.constantsService.uniqueIdForProfilePic}`;
  }

  setDefaultProfilePicture() {
    let imageElement: HTMLImageElement = document.getElementById('userProfilePic') as HTMLImageElement;
    imageElement.src = 'assets/imgs/DefaultPic.png';
  }

  chooseTheme(theme: string) {
    this.appThemeService.chooseTheme(theme);
    this.constantsService.savedTheme = theme;
    localStorage.setItem('theme', theme);
  }

  setProfilePic() {
    this.resizeMainImage(this.croppedImage).subscribe(smallerImage => {
      this.getDataService.saveProfilePic(smallerImage).subscribe(resultURL => {
        this.constantsService.setUniqueIdForProfilePic();

        this.status = 'success'
        setTimeout(() => {
          this.status = null;
        }, 2000)
      }, e => {
        this.status = 'failed'
        setTimeout(() => {
          this.status = null;
        }, 2000)
        console.error('failed to save profile pic' + JSON.stringify(e))
      })
    })
  }

  openProfilePicModal() {
    this.imageChangedEvent = null;

    this.modalService.open(this.cropperModal, { windowClass: 'cropperModal', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {

    }, (reason) => {
      this.modalService.dismissAll();
    });
  }

  goToSimpleExamplePage(){
    this.router.navigate(['/simpleExample'])
  }

  async switchDealerGroup() {
    const modalRef = this.modalService.open(DealerGroupSelectionModalComponent, {
      windowClass: "dealerGroupSelectionModal",
      size: 'md',
      keyboard: true,
      ariaLabelledBy: 'modal-basic-title'
    })

    modalRef.componentInstance.dealerGroups = this.selectionsService.userDealerGroups;

    try {
      // Await the modal result
      const selectedDealerGroup: DealerGroupVM = await modalRef.result;

      if (selectedDealerGroup) {
        // Perform your async operation with await
        const res = await this.getDataService.switchDealerGroup(selectedDealerGroup.Id);
        const tokenContents: AccessToken = this.appStartService.parseJwt(res.accessToken);

        // Update local storage
        localStorage.setItem('accessToken', res.accessToken);
        localStorage.setItem('refreshToken', res.refreshToken);

        // Start the app and reload the window
        this.appStartService.startApp(res.accessToken);
        window.location.reload();
      }
    } catch (reason) {
      console.log('Modal dismissed', reason);
    }
  }

  resizeMainImage(picture: string): any {
    const observable = new Observable(observer => {
      let ctx = (<HTMLCanvasElement>this.resizeMainImageCanvas.nativeElement).getContext('2d');
      let qualityConverted = 300  //
      ctx.canvas.width = qualityConverted;
      ctx.canvas.height = qualityConverted;

      const drawImageOnCanvas = (img) => {
        ctx.drawImage(img, 0, 0, ctx.canvas.width, ctx.canvas.height);
        return ctx.canvas.toDataURL('image/jpeg', 0.6);
      }

      const drawImageOntoCanvas = () => {
        var newDataUri = drawImageOnCanvas(img)
        observer.next(newDataUri);
        observer.complete();
      }

      let img = new Image;
      img.onload = drawImageOntoCanvas;
      img.src = picture;
    })
    return observable
  }

  fileChangeEvent(event: any): void {
    this.imageChangedEvent = event;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }

  changePassword() {
    this.router.navigateByUrl('changePassword');
  }

  goToPublicHolidays() {
    this.router.navigateByUrl('publicHolidays');
  }

  logout() {
    this.appStartService.logout();
  }





}
