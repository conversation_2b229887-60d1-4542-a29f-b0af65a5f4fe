using CPHI.Spark.Loader.Services;
using CPHI.Spark.Loader.Services.GenericDealsLoader;
using CPHI.Spark.Model;
using CPHI.Spark.Model.FltOrd;
using CPHI.Spark.Model.Import;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Quartz;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.SqlTypes;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Text.RegularExpressions;

namespace CPHI.Spark.Loader.GenericJobs.Deals
{


   public class CarCo_DealsJobService : IGenericDealsJobServiceParams
   {

      public int SiteId { get; set; }
      public int RetailerSiteId { get; set; }
      public int RowsToSkip { get; set; }
      public DealerGroupName DealerGroupName { get; set; }
      public string FileExt { get; set; }
      public string DbConnectionName { get; set; }
      public string DbConnectionString { get; set; }
      public string[] AllMatchingFiles { get; set; }
      public string FinalSPToRun { get; set; }
      public string ImportTableName { get; set; }
      public string ImportTableMergeSPName { get; set; }
      public bool TriggerUpdate { get; set; }
      public string JobName { get; set; }
      public CarCo_DealsJobService(string incomingRoot)
      {
         GetMatchingFilesAndImportParams(incomingRoot);
      }

      public List<string> GetUniqueSalesmen(List<List<string>> rows, Dictionary<string, int> headerDictionary)
      {
         List<string> allSalesmen = new List<string>();
         int index = headerDictionary["SE"];

         foreach (var row in rows)
         {
            string salesperson = row[index];
            allSalesmen.Add(salesperson);
         }

         var distinct = allSalesmen.Distinct().ToList();
         return distinct;
      }

      public List<string> GetUniqueMakes(List<List<string>> rows, Dictionary<string, int> headerDictionary)
      {
         List<string> distinctMakes = ["RENAULT", "DACIA", "ALPINE"];
         return distinctMakes;
      }


      public void GetMatchingFilesAndImportParams(string incomingRoot)
      {
         DealerGroupName = DealerGroupName.Carco;
         RowsToSkip = 0;
         JobName = string.Concat(DealerGroupName.Carco.ToString(), "DealsJob");
         FileExt = ".csv";
         DbConnectionString = ConfigService.autopriceConnectionString;
         DbConnectionName = "AutoPriceConnection";
         AllMatchingFiles = Directory.GetFiles(incomingRoot, $"*Dealbook Extract 365*.csv*");
         TriggerUpdate = true;
         SiteId = 266;
         RetailerSiteId = 256;
         ImportTableName = "Deals_CarCo";
         ImportTableMergeSPName = "MERGE_Deals_CarCo";
      }



      public DataTable ConvertToDataTable(List<Deal> toReturn)
      {
         var dataTable = toReturn.ToDataTable();

         dataTable.Columns.Remove("Salesman");
         dataTable.Columns.Remove("Site");
         dataTable.Columns.Remove("VehicleClass");
         dataTable.Columns.Remove("Franchise");
         dataTable.Columns.Remove("OrderType");
         dataTable.Columns.Remove("VehicleType");
         dataTable.Columns.Remove("DeliverySite");
         dataTable.Columns.Remove("Comments");
         dataTable.Columns.Remove("VehicleAdvert");


         return dataTable;
      }

      public DateTime GetCurrentMonth(string fileName)
      {
         // Extract the year and month from the part before ".csv"
         // Find the position of the '-' character
         int startIndex = fileName.IndexOf('-') + 1;

         // Find the position of the ' Sales' substring
         int endIndex = fileName.IndexOf(" Sales");

         // Extract the substring between the '-' and ' Sales'
         string yearMonthPart = fileName.Substring(startIndex, endIndex - startIndex);

         string monthName = yearMonthPart.Split(" ")[0];
         // Parse the year and month
         int year = int.Parse(yearMonthPart.Split(" ")[1]);

         DateTime parsedDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);

         // Extract the month number from the parsed DateTime object
         int month = parsedDate.Month;

         // Create DateTime for the first day of that month
         DateTime resultDate = new DateTime(year, month, 1);
         return resultDate;
      }

      public Deal ConvertRowToDeal(List<string> rowCells, GenericDealsLoaderDbLookups lookups, Dictionary<string, int> headerDictionary)
      {
         // Ensure the deal is confirmed
         string conf = rowCells[headerDictionary["CONF"]];
         if (conf.ToUpper().Trim() != "YES") { return null; }

         Deal_CarCo newRow = new Deal_CarCo();

         // This is the unique key
         newRow.EnquiryNumber = RowInterpretationService.GetInt(rowCells[headerDictionary["DEALID"]]);

         if (newRow.EnquiryNumber == 0)
         {
            throw new Exception("Invalid EnquiryNumber");
         }

         string branch = rowCells[headerDictionary["BRANCH"]].Trim();

         newRow.Site_Id = GetSiteId(branch);

         string newUsed = rowCells[headerDictionary["NEW/USED"]];
         if (newUsed == string.Empty) { return null; }

         newRow.VehicleType_Id = newUsed.Trim().ToUpper() == "NEW" ? 37 : 38;

         //// -----

         string stockNumber = rowCells[headerDictionary["STOCK NO"]];
         newRow.StockNumber = stockNumber;

         string vrm = rowCells[headerDictionary["REG NO"]];
         newRow.Reg = vrm;

         string cust = rowCells[headerDictionary["CUSTOMER"]];
         newRow.Customer = cust;

         string model = rowCells[headerDictionary["MODEL"]];
         newRow.Model = model.Length > 50 ? model.Substring(0, 50) : model;
         newRow.Description = model.Length > 50 ? model.Substring(0, 50) : model;

         newRow.VehicleClass_Id = 287; // Set to Car
         newRow.OrderType_Id = 22; // Set to Retail

         var franchiseObj = lookups.Franchises.FirstOrDefault(x => branch.ToUpper().Contains(x.Description.ToUpper()));

         if (franchiseObj == null)
         {
            newRow.Franchise_Id = 334; // Set to Unknown
         }
         else
         {
            newRow.Franchise_Id = franchiseObj.Id;
         }

         DateTime orderDate = RowInterpretationService.GetDateFromString(rowCells[headerDictionary["ORDERDATE"]]);
         newRow.OrderDate = orderDate;

         // Salesman
         string salesman = rowCells[headerDictionary["SE"]];

         if (salesman == null || salesman == string.Empty || salesman == " ")
         {
            throw new Exception("No salesman");
         }

         var salesmanObj = lookups.Salesmen.FirstOrDefault(x => x.DmsId == salesman);

         if (salesmanObj == null)
         {
            throw new Exception("Salesman not found");
         }

         newRow.Salesman_Id = salesmanObj.Id;

         // Delivery Date
         DateTime delivDate = RowInterpretationService.GetDateFromString(rowCells[headerDictionary["DELIV DATE"]]);
         newRow.ActualDeliveryDate = delivDate;

         // Assigning this as the generic loader requires this for the month
         // newRow.InvoiceDate = delivDate;

         // Required for MERGE to work
         newRow.AccountingDate = delivDate;

         // IsDelivered
         string delivStatus = rowCells[headerDictionary["DELIV STATUS"]];

         if (delivStatus.ToUpper().Trim() == "DLV")
         {
            newRow.IsDelivered = true;
         }
         else
         {
            newRow.IsDelivered = false;
         }

         string actChassisProfitStr = rowCells[headerDictionary["ACT CHASSIS PROFIT"]];
         string netPriceStr = rowCells[headerDictionary["NET PRICE"]];

         decimal actChassisProfit = RowInterpretationService.GetDecimal(actChassisProfitStr);
         decimal netPrice = RowInterpretationService.GetDecimal(netPriceStr);

         newRow.Sale = netPrice;
         newRow.CoS = (netPrice - actChassisProfit) * -1;

         string actAddBonusStr = rowCells[headerDictionary["ACT ADD BONUS"]];
         newRow.NewBonus1 = RowInterpretationService.GetDecimal(actAddBonusStr);

         string actAddBonus2Str = rowCells[headerDictionary["ACT METAL SUBSIDY"]];
         newRow.NewBonus2 = RowInterpretationService.GetDecimal(actAddBonus2Str);

         string mechPrepStr = rowCells[headerDictionary["ACT RECON/OTHER COSTS"]];
         newRow.MechPrep = RowInterpretationService.GetDecimal(mechPrepStr);

         string discountStr = rowCells[headerDictionary["ACT OALLOW/DISCOUNT"]];
         newRow.Discount = RowInterpretationService.GetDecimal(discountStr);

         string accessoryProfitStr = rowCells[headerDictionary["ACT ACCESSORY PROFIT"]];
         newRow.AccessoriesSale = RowInterpretationService.GetDecimal(accessoryProfitStr);

         string warrantyCostStr = rowCells[headerDictionary["ACT WARRANTY COST"]];
         newRow.WarrantySale = RowInterpretationService.GetDecimal(warrantyCostStr);

         // Both of these sum to FinanceCommission
         string financeIncomeStr = rowCells[headerDictionary["ACT FINANCE INCOME"]];
         newRow.FinanceCommission = RowInterpretationService.GetDecimal(financeIncomeStr);

         string financeMBStr = rowCells[headerDictionary["ACT FINANCE MB"]];
         newRow.FinanceCommission += RowInterpretationService.GetDecimal(financeMBStr); // Summing up if both apply

         string tyreInsIncomeStr = rowCells[headerDictionary["ACT TYRE INS INCOME"]];
         newRow.TyreInsuranceCommission = RowInterpretationService.GetDecimal(tyreInsIncomeStr);

         if (newRow.TyreInsuranceCommission != 0)
         {
            newRow.HasTyreInsurance = true;
         }

         string financeSubsidyStr = rowCells[headerDictionary["ACT FINANCE SUBSIDY"]];
         newRow.FinanceSubsidy = RowInterpretationService.GetDecimal(financeSubsidyStr);

         // Both of these sum to CosmeticInsuranceCommission
         string cpiIncomeStr = rowCells[headerDictionary["ACT CPI INCOME"]];
         newRow.CosmeticInsuranceCommission = RowInterpretationService.GetDecimal(cpiIncomeStr);

         string smartRepairStr = rowCells[headerDictionary["ACT SMART REPAIR"]];
         newRow.CosmeticInsuranceCommission += RowInterpretationService.GetDecimal(smartRepairStr); // Summing up

         if (newRow.CosmeticInsuranceCommission != 0)
         {
            newRow.HasCosmeticInsurance = true;
         }

         string gapRtiIncomeStr = rowCells[headerDictionary["ACT GAP/RTI INCOME"]];
         newRow.GapInsuranceCommission = RowInterpretationService.GetDecimal(gapRtiIncomeStr);

         if (newRow.GapInsuranceCommission != 0)
         {
            newRow.HasGapInsurance = true;
         }

         string paintProtectionStr = rowCells[headerDictionary["ACT PAINT PROTECTION"]];
         newRow.PaintProtectionSale = RowInterpretationService.GetDecimal(paintProtectionStr);

         string actWarrantyStr = rowCells[headerDictionary["ACT WARRANTY"]];
         newRow.WarrantySale += RowInterpretationService.GetDecimal(actWarrantyStr); // Summing up if both apply

         if (newRow.WarrantySale != 0)
         {
            newRow.HasWarranty = true;
         }

         string financeType = rowCells[headerDictionary["HP TYPE"]];

         newRow.FinanceType = financeType;


         if (newRow.FinanceType.ToUpper().Trim() != "CASH")
         {
            newRow.IsFinanced = true;
         }

         decimal addOnProfit =
          (decimal)(newRow.CosmeticInsuranceSale) + (decimal)(newRow.CosmeticInsuranceCost) + (decimal)(newRow.CosmeticInsuranceCommission) +
          (decimal)(newRow.GapInsuranceSale) + (decimal)(newRow.GapInsuranceCost) + (decimal)(newRow.GapInsuranceCommission) +
          (decimal)(newRow.PaintProtectionSale) + (decimal)(newRow.PaintProtectionCost) + (decimal)(newRow.ServicePlanSale) +
          (decimal)(newRow.ServicePlanCost) + (decimal)(newRow.WarrantySale) + (decimal)(newRow.WarrantyCost) +
          (decimal)(newRow.WheelGuardSale) + (decimal)(newRow.WheelGuardCost) + (decimal)(newRow.WheelGuardCommission) +
          (decimal)(newRow.TyreInsuranceSale) + (decimal)(newRow.TyreInsuranceCost) + (decimal)(newRow.TyreInsuranceCommission) +
          (decimal)(newRow.AlloyInsuranceSale) + (decimal)(newRow.AlloyInsuranceCost) + (decimal)(newRow.AlloyInsuranceCommission) +
          (decimal)(newRow.TyreAndAlloyInsuranceSale) + (decimal)(newRow.TyreAndAlloyInsuranceCost) + (decimal)(newRow.TyreAndAlloyInsuranceCommission);

         decimal otherProfit =
          (decimal)(newRow.AccessoriesSale) + (decimal)(newRow.AccessoriesCost) + (decimal)(newRow.FuelSale) + (decimal)(newRow.FuelCost) +
          (decimal)(newRow.BrokerCost) + (decimal)(newRow.IntroCommission) + (decimal)(newRow.OemDeliverySale) + (decimal)(newRow.OemDeliveryCost) +
          (decimal)(newRow.PDICost) + (decimal)(newRow.MechPrep) + (decimal)(newRow.BodyPrep) + (decimal)(newRow.Other) +
          (decimal)(newRow.Error) + (decimal)(newRow.StandardWarrantyCost) + (decimal)(newRow.PaintProtectionAccessorySale) +
          (decimal)(newRow.PaintProtectionAccessoryCost);

         decimal metalProfit =
          (decimal)(newRow.Sale) + (decimal)(newRow.Discount) +
          (decimal)(newRow.VatCost) + (decimal)(newRow.NewBonus1) + (decimal)(newRow.NewBonus2)
          + (decimal)(newRow.CoS);

         decimal financeProfit =
          (decimal)(newRow.FinanceCommission) + (decimal)(newRow.FinanceSubsidy) + (decimal)(newRow.SelectCommission) +
          (decimal)(newRow.RCIFinanceCommission) + (decimal)(newRow.StandardsCommission) + (decimal)(newRow.ProPlusCommission);

         newRow.TotalNLProfit = (financeProfit + metalProfit + otherProfit + addOnProfit);

         newRow.Units = 1;
         newRow.IsRemoved = false;
         newRow.DeliverySite_Id = null;

         newRow.HasServicePlan = false;
         newRow.HasPaintProtectionAccessory = false;
         newRow.HasPaintProtection = false;
         newRow.HasShortWarranty = false;
         newRow.HasWheelGuard = false;
         newRow.HasAlloyInsurance = false;
         newRow.HasTyreAndAlloyInsurance = false;

         newRow.CreatedDate = DateTime.Now;
         newRow.LastUpdated = DateTime.Now;
         newRow.WhenNew = DateTime.Now;

         Deal result = new Deal(newRow);
         return result;
      }

      private static int GetSiteId(string branch)
      {
         if (branch.Contains("City Motors Renault"))
         {
            return 267;
         }
         else if (branch.Contains("City Motors Dacia"))
         {
            return 267;
         }
         else if (branch.Contains("Alpine"))
         {
            return 268;
         }
         else if (branch.Contains("LCV"))
         {
            return 270;
         }
         else if (branch.Contains("Renew"))
         {
            return 269;
         }
         // S J Cook & Sons
         else
         {
            return 266;
         }
      }

      public Dictionary<string, int> BuildHeaderDictionary(List<string> headers)
      {

         List<string> colHeaders = new List<string>()
            {
                "BRANCH",
                "CONF",
                "NEW/USED",
                "SUPP",
                "STOCK NO",
                "REG NO",
                "CUSTOMER",
                "MODEL",
                "ORDERDATE",
                "DAYS IN STOCK",
                "SE",
                "DELIV DATE",
                "DELIV STATUS",
                "ACT CHASSIS PROFIT",
                "ACT ADD BONUS",
                "ACT METAL SUBSIDY",
                "ACT RECON/OTHER COSTS",
                "ACT OALLOW/DISCOUNT",
                "ACT ACCESSORY PROFIT",
                "ACT WARRANTY COST",
                "ACT TOTAL VEHICLE PROFIT",
                "ACT FINANCE INCOME",
                "ACT FINANCE MB",
                "ACT TYRE INS INCOME",
                "ACT FINANCE SUBSIDY",
                "ACT CPI INCOME",
                "ACT SMART REPAIR",
                "ACT GAP/RTI INCOME",
                "ACT PAINT PROTECTION",
                "ACT WARRANTY",
                "ACT TOTAL F&I INCOME",
                "ACT TOTAL GROSS PROFIT",
                "CONFIRM NOTES",
                "HP TYPE",
                "CP TYPE",
                "FINANCE CO",
                "LOAN AMOUNT",
                "PCP MFV",
                "PER MONTH",
                "HP MONTHS",
                "RATE",
                "BASE RATE",
                "GAP PREMIUM",
                "GAP POLICY NO",
                "TYRE PREMIUM",
                "TYRE POLICY NO",
                "REFERRAL",
                "WARRANTY PREMIUM",
                "WARRANTY POLICY NO",
                "COSMETIC PREMIUM",
                "COSMETIC POLICY NO",
                "PCP MPA",
                "PEX ALLOWANCE",
                "PEX SETTLEMENT",
                "PEX REG NO",
                "PEX ALLOWANCE2",
                "PEX SETTLEMENT2",
                "PEX REG NO2",
                "ACT ALLOY INSURANCE",
                "ACT T&A INSURANCE",
                "T&A PREMIUM",
                "T&A POLICY NO",
                "ALLOY PREMIUM",
                "ALLOY POLICY NO",
                "PORTAL REFERRAL",
                "ACT KEY COVER",
                "KEY COVER PREMIUM",
                "KEY COVER POLICY NO",
                "BASIC",
                "OPTIONS",
                "NET PRICE",
                "BASIC MARGIN",
                "OPTIONS MARGIN",
                "WHOLESALE",
                "FINANCE DEPOSIT",
                "FINANCE AMOUNT",
                "DEPOSIT",
                "CUSTOMER ID",
                "REGISTERED DATE",
                "INVOICE DATE",
                "SALES TYPE",
                "SALES SUB-TYPE",
                "DEALID",
                "ENQUIRYID",
                "ORDERID",
                "ORDERDELIVDAYS",
                "BUSINESSMANAGER",
                "HOUSECHARGEMARGIN",
                "ACT OTHER INSURANCE",
                "PASSEDFINANCE",
                "PX FINANCE COMPANY",
                "PX AGREEMENT NUMBER",
                "NON-OP",
                "VEHICLE TYPE",
                "VIN"
            };

         Dictionary<string, int> result = new Dictionary<string, int>();

         List<string> headersUpper = headers.Select(x => x.ToUpper()).ToList();

         foreach (string colHeader in colHeaders)
         {
            result.Add(colHeader, headersUpper.IndexOf(colHeader));
         }

         return result;
      }


   }
}
