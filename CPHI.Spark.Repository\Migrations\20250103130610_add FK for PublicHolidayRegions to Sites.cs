﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class addFKforPublicHolidayRegionstoSites : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PublicHolidayRegionId",
                table: "Sites",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Sites_PublicHolidayRegionId",
                table: "Sites",
                column: "PublicHolidayRegionId");

            migrationBuilder.AddForeignKey(
                name: "FK_Sites_PublicHolidayRegions_PublicHolidayRegionId",
                table: "Sites",
                column: "PublicHolidayRegionId",
                principalTable: "PublicHolidayRegions",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Sites_PublicHolidayRegions_PublicHolidayRegionId",
                table: "Sites");

            migrationBuilder.DropIndex(
                name: "IX_Sites_PublicHolidayRegionId",
                table: "Sites");

            migrationBuilder.DropColumn(
                name: "PublicHolidayRegionId",
                table: "Sites");
        }
    }
}
