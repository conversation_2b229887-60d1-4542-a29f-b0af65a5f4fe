using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public class PriceChangesService
   {
      private readonly string _connString;
      public PriceChangesService(string connString)
      {
         this._connString = connString;
      }

      public async Task<List<RetailerSite>> GetRetailersToCheckForChanges(DealerGroupName dealerGroup)
      {
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(_connString);
         var publicHolidaysDataAccess = new PublicHolidaysDataAccess(_connString);

         Dictionary<int, bool> publicHols = await publicHolidaysDataAccess.GetBankHolidayDictionaryForRetailerSites((int)dealerGroup, DateTime.Today);
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

         retailers = GetRetailersEliglibleForTodayUpdate(retailers, publicHols);
         return retailers;
      }

      public async Task<GetTodayChangesResponse> getTodayChangesForUpdateWebsite(DealerGroupName dealerGroup, GetPriceChangesNewParams parms)
      {

         PriceChangesDataAccess priceChangesDataAccess = new PriceChangesDataAccess(_connString);

         
         //-----------------------------
         // 1. Auto Changes
         //-----------------------------
         IEnumerable<PricingChangeNew> todayAutoChanges = await priceChangesDataAccess.GetAutoPriceChanges(parms);

         //Identify if there are any that now need updating
         List<PricingChangeNew> changesToMaybeAction = todayAutoChanges
             .Where(x => !x.IsOptedOutOnDay && x.DateConfirmed == null)
             .Where(x => x.IsSetToAutoUpdatePrice)
             .ToList();

         //Further filter down
         var overdueChanges = changesToMaybeAction.Where(x => DateTime.Now > x.WhenToActionChangesEachDay).ToList();
         var remainingUnoverdue = changesToMaybeAction.Except(overdueChanges).ToList();
         var approvedChangesToAction = remainingUnoverdue.Where(x => x.ApprovedDate.HasValue).ToList();
         var totalChanges = approvedChangesToAction.Concat(overdueChanges).ToList();

         var overdues = overdueChanges.ConvertAll(x => new PricingChangeMinimal(x,true));
         var approved = approvedChangesToAction.ConvertAll(x => new PricingChangeMinimal(x,true));
         var total = totalChanges.ConvertAll(x => new PricingChangeMinimal(x,true));


         //-----------------------------
         // 2. Manual Changes
         //-----------------------------
         var todayManualChanges = await priceChangesDataAccess.GetManualPriceChanges(parms.RetailerSiteIds, parms.DealerGroupId);
         var unconfirmedManualChanges = todayManualChanges.Where(x => x.DateConfirmed == null).ToList();
         approved.AddRange(unconfirmedManualChanges);
         total.AddRange(unconfirmedManualChanges);


         return new GetTodayChangesResponse()
         {
            overdueChanges = overdues,
            approvedChangesToAction = approved,
            totalChanges = total
         };
      }



      private List<RetailerSite> GetRetailersEliglibleForTodayUpdate(List<RetailerSite> retailers, Dictionary<int, bool> publicHolsForDg)
      {

         List<RetailerSite> eligibleRetailers = new List<RetailerSite>();

         foreach (var retailerSite in retailers)
         {

            //continue if this site isn't signed up to auto update prices
            if (!retailerSite.UpdatePricesAutomatically)
            {
               continue;
            }

            //skip on if this site isn't signed up to change prices today
            var dayOfWeek = DateTime.Now.DayOfWeek;
            switch (dayOfWeek)
            {
               case DayOfWeek.Sunday:
                  { if (!retailerSite.UpdatePricesSun) { continue; } break; }
               case DayOfWeek.Monday:
                  { if (!retailerSite.UpdatePricesMon) { continue; } break; }
               case DayOfWeek.Tuesday:
                  { if (!retailerSite.UpdatePricesTue) { continue; } break; }
               case DayOfWeek.Wednesday:
                  { if (!retailerSite.UpdatePricesWed) { continue; } break; }
               case DayOfWeek.Thursday:
                  { if (!retailerSite.UpdatePricesThu) { continue; } break; }
               case DayOfWeek.Friday:
                  { if (!retailerSite.UpdatePricesFri) { continue; } break; }
               case DayOfWeek.Saturday:
                  { if (!retailerSite.UpdatePricesSat) { continue; } break; }
            }

            bool isBankHoliday = publicHolsForDg[retailerSite.Id];

            //skip on if site hasn't signed up to do public holidays and today is a public holiday
            if (!retailerSite.UpdatePricesPubHolidays && isBankHoliday)
            {
               continue;
            }

            eligibleRetailers.Add(retailerSite);
         }

         return eligibleRetailers;


      }



      public async Task<GetPriceChangesNewParams> CreateParmsToGetChanges(DealerGroupName dealerGroup)
      {
         //get all the price changes
         var sitesDataAccess = new RetailerSitesDataAccess(_connString);
         var bandingsDict = await sitesDataAccess.GetRetailerStrategyBandings();
         List<int> retailerIds = new();
         foreach (var banding in bandingsDict)
         {
            retailerIds.Add(banding.Key);
         }
         string retailerSiteIds = string.Join(',',  retailerIds);

         //Retrieve price changes
         var parms = new GetPriceChangesNewParams
         {
            ChosenDate = DateTime.Today,
            RetailerSiteIds = retailerSiteIds,
            IncludeNewVehicles = true,
            IncludeSmallChanges = false,
            DealerGroupId = (int)dealerGroup,
            BandingsDict = bandingsDict,
            IncludeUnPublishedAds = false,
            IncludeUnPublishedAdBasedOnSiteSetting = true,
            IncludeNewVehiclesBasedOnSiteSetting = true,
            IncludeLifecycleStatusesBasedOnSiteSetting = true,
            FilterDaysInStockBasedOnSiteSetting = false,
            IncludeVehicleTypesBasedOnSiteSetting = true
         };
         return parms;
      }
   }
}
