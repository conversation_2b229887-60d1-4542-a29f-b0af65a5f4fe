-- SPK-5014 Populate PublicHolidayRegions
-- Based on RRG database; may need to be tweaked for other dbs

-- ======= 1. Set Regions
INSERT INTO PublicHolidayRegions
VALUES
('England and Wales'), -- 1
('Scotland'), -- 2
('Northern Ireland'), -- 3
('Spain'), -- 4
('USA'); -- 5

-- ======= 2. INSERT Holidays for England & Wales
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-01-01';

-- Good Friday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-04-18';

-- Easter Monday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-04-21';

-- Early May Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-05-05';

-- Spring Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-05-26';

-- Summer Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-08-25';

-- Christmas Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-12-25';

-- Boxing Day (substitute)
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 1 FROM PublicHolidays WHERE Date = '2025-12-26';

-- ======= 3. Holidays for Scotland
-- Insert Scotland-specific holidays into PublicHolidays
INSERT INTO PublicHolidays (Date, DealerGroup_Id)
VALUES 
    ('2025-01-02', 1),  -- 2nd January (substitute)
    ('2025-08-04', 1),  -- Summer Bank Holiday (Scotland)
    ('2025-12-01', 1);  -- St Andrew's Day (substitute)

-- New Year's Day (substitute)
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-01-01';

-- 2nd January (substitute) --
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-01-02';

-- Good Friday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-04-18';

-- Early May Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-05-05';

-- Summer Bank Holiday (Scotland) ---
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-08-04';

-- St Andrew's Day (substitute) --
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-12-01';

-- Christmas Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-12-25';

-- Boxing Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 2 FROM PublicHolidays WHERE Date = '2025-12-26';

-- ======= 3. Holidays for Northern Ireland
INSERT INTO PublicHolidays (Date, DealerGroup_Id)
VALUES 
    ('2025-03-17', 1),  -- St Patrick's Day
    ('2025-07-14', 1);  -- Battle of the Boyne (substitute in 2025)

-- New Year's Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-01-01';

-- St Patrick's Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-03-17';

-- Good Friday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-04-18';

-- Easter Monday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-04-21';

-- Early May Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-05-05';

-- Spring Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-05-26';

-- Battle of the Boyne (Orangemen’s Day substitute)
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-07-14';

-- Summer Bank Holiday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-08-25';

-- Christmas Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-12-25';

-- Boxing Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 3 FROM PublicHolidays WHERE Date = '2025-12-26';

-- ====== 4. Spain
-- Spain national public holidays
INSERT INTO PublicHolidays (Date, DealerGroup_Id)
VALUES 
    ('2025-01-06', 1),  -- Epiphany
    ('2025-05-01', 1),  -- Labour Day
    ('2025-08-15', 1),  -- Assumption of Mary
    ('2025-10-12', 1),  -- National Day
    ('2025-11-01', 1),  -- All Saints' Day
    ('2025-12-06', 1),  -- Constitution Day
    ('2025-12-08', 1);  -- Immaculate Conception

-- New Year's Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-01-01';

-- Epiphany (Three Kings)
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-01-06';

-- Good Friday
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-04-18';

-- Labour Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-05-01';

-- Assumption of Mary
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-08-15';

-- National Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-10-12';

-- All Saints’ Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-11-01';

-- Constitution Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-12-06';

-- Immaculate Conception
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-12-08';

-- Christmas Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 4 FROM PublicHolidays WHERE Date = '2025-12-25';

--- 5. USA
INSERT INTO PublicHolidays (Date, DealerGroup_Id)
VALUES 
    ('2025-01-20', 1),  -- Martin Luther King Jr. Day
    ('2025-02-17', 1),  -- Presidents' Day
    ('2025-05-26', 1),  -- Memorial Day
    ('2025-07-04', 1),  -- Independence Day
    ('2025-09-01', 1),  -- Labor Day
    ('2025-10-13', 1),  -- Columbus Day
    ('2025-11-11', 1),  -- Veterans Day
    ('2025-11-27', 1);  -- Thanksgiving Day

-- New Year's Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-01-01';

-- Martin Luther King Jr. Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-01-20';

-- Presidents' Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-02-17';

-- Memorial Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-05-26';

-- Independence Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-07-04';

-- Labor Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-09-01';

-- Columbus Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-10-13';

-- Veterans Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-11-11';

-- Thanksgiving Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-11-27';

-- Christmas Day
INSERT INTO PublicHolidayRegionMaps (PublicHolidayId, PublicHolidayRegionId)
SELECT Id, 5 FROM PublicHolidays WHERE Date = '2025-12-25';

--- 6. Set RegionIds for Renault UK (all sites are England and Wales)
UPDATE
Sites 
SET PublicHolidayRegionId = 1
WHERE DealerGroup_Id = 1




