using CPHI.Repository;
using log4net;
using log4net.Config;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.EventLog;
using System.IO;
using System.Reflection;

namespace CPHI.Spark.Loader
{
    public class Program
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public static void Main(string[] args)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            var logRepository = LogManager.GetRepository(Assembly.GetEntryAssembly());
            System.IO.Directory.SetCurrentDirectory(System.AppDomain.CurrentDomain.BaseDirectory);
            XmlConfigurator.Configure(logRepository, new FileInfo("log4net.config"));

            CreateHostBuilder(args).Build().Run();
            { }
        }

        //public Program(IConfiguration configuration)
        //{
        //    Configuration = configuration;
        //}

        


        public static IHostBuilder CreateHostBuilder(string[] args) =>
         Host.CreateDefaultBuilder(args)
           .ConfigureLogging(
             options => options.AddFilter<EventLogLoggerProvider>(level => level >= LogLevel.Information))
           .ConfigureServices((hostContext, services) =>
           {
               services.AddHostedService<SchedulingService>()
           .Configure<EventLogSettings>(config =>
           {
               config.LogName = "Loader Service";
               config.SourceName = "Loader Service Source";
           });

           }).ConfigureAppConfiguration((hostingContext, config) =>
           {
               //string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
               //var env = hostingContext.HostingEnvironment;
               

               config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                    // .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true); // optional extra provider

               ConfigService.configurationBuilder = config;
               CPHIDbContext.envi = "loaderProject"; // to tell the repository project not to try adding a second appsettings
               ConfigService.Init();
               
           }).UseWindowsService();



    }

}
