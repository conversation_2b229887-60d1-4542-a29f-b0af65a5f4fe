import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridOptions, ColDef } from 'ag-grid-community';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { PublicHolidayResponse, PublicHolidayViewModel, PublicHolidayRegion } from 'src/app/model/publicHoliday.model';
import { PublicHolidayModalComponent } from './publicHolidayModal.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { Router } from '@angular/router';

interface CellRendererParams {
  data: PublicHolidayViewModel;
  value: string;
}

@Component({
  selector: 'app-public-holidays',
  template: `
    <div id="overallHolder"[ngClass]="constants.environment.customer">>
      <div class="row">
        <div class="col-12">
          <div style="margin-left: 20%;">
            <h2>Public Holidays Management</h2>

            <div class="mb-3">
              <button class="btn btn-primary" (click)="openAddModal()">
                <i class="fas fa-plus"></i> Add Public Holiday
              </button>
            </div>
          </div>

          <div class="position-relative">
            <ag-grid-angular 
              class="ag-theme-balham" 
              [gridOptions]="gridOptions"
              [rowData]="rowData">
            </ag-grid-angular>
          </div>
        </div>
      </div>
    </div>

    <publicHolidayModal #publicHolidayModal></publicHolidayModal>
  `,
  styleUrls: ['../../../styles/components/_agGrid.scss'],
  styles: [`
    .container-fluid {
      padding: 20px;
    }
    
    h2 {
      margin-bottom: 20px;
      color: white;
    }
    
    .btn {
      margin-right: 10px;
    }
    
    ag-grid-angular {
      height: 450px;
      width: 75%;
      margin: 0 auto;
      display: block;
    }
    
    #excelExport {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      cursor: pointer;
    }
  `]
})
export class PublicHolidaysComponent implements OnInit {
  @ViewChild('publicHolidayModal') publicHolidayModal: PublicHolidayModalComponent;

  private destroy$ = new Subject<void>();
  
  rowData: PublicHolidayViewModel[] = [];
  regions: PublicHolidayRegion[] = [];
  
  gridOptions: GridOptions = {
    columnDefs: this.getColumnDefs(),
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true
    },
    rowSelection: 'single',
    animateRows: true,
    columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
    },
    onGridReady: () => {
      this.gridOptions.api?.sizeColumnsToFit();
    }
  };

  constructor(
    public constants: ConstantsService,
    private getDataMethodsService: GetDataMethodsService,
    private columnTypeService: ColumnTypesService,
    private modalService: NgbModal,
    private selectionsService: SelectionsService,
    private router: Router
  ) { }

  ngOnInit(): void {

    if (this.selectionsService.user.RoleName !== 'System Administrator') {
      // Redirect to dashboard 
      this.router.navigate(['/dashboard']);
      return;
    }

    this.loadRegions();

    // Listen for global events from ag-grid buttons
    document.addEventListener('deleteHoliday', (event: CustomEvent<{id: number}>) => {
      this.deleteHoliday(event.detail.id);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getColumnDefs(): ColDef[] {
    return [
      {
        headerName: 'Date',
        field: 'Date',
        width: 150,
        type: 'dateLongYear',
      },
      {
        headerName: 'Regions',
        field: 'RegionNames',
        width: 300,
        valueFormatter: (params) => {
          if (params.value && Array.isArray(params.value)) {
            return params.value.join(', ');
          }
          return '';
        }
      },
      {
        headerName: 'Actions',
        width: 150,
        cellRenderer: (params: CellRendererParams) => {
          return `
            <button class="btn btn-xs btn-danger" onclick="window.deleteHoliday(${params.data.Id})" style="font-size: 11px; padding: 2px 6px;">
              <i class="fas fa-trash"></i>
            </button>
          `;
        }
      }
    ];
  }

  loadData(): void {
    this.getDataMethodsService.getPublicHolidays()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (holidays) => {
          const mappedData = holidays.map(holiday => this.mapToViewModel(holiday));
          this.rowData = [...mappedData]; 
        },
        error: (error) => {
          console.error('Error loading public holidays:', error);
        }
      });
  }

  loadRegions(): void {
    this.getDataMethodsService.getPublicHolidayRegions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (regions) => {
          this.regions = regions;
          this.loadData();
        },
        error: (error) => {
          console.error('Error loading regions:', error);
        }
      });
  }

  private mapToViewModel(holiday: PublicHolidayResponse): PublicHolidayViewModel {

    // Handle case where RegionIds might be undefined or null
    const regionIds = holiday.RegionIds || [];

    const regionNames = regionIds.map((regionId: number) => {
      const region = this.regions.find((r: PublicHolidayRegion) => r.Id === regionId);
      return region ? region.Region : `Region ${regionId}`;
    });

    return {
      Id: holiday.Id,
      Date: holiday.Date,
      RegionNames: regionNames,
      RegionIds: regionIds
    };
  }

  openAddModal(): void {
    this.publicHolidayModal.openModal(this.regions);
    this.publicHolidayModal.holidaySaved
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadData();
      });
  }



  deleteHoliday(id: number): void {
    if (confirm('Are you sure you want to delete this public holiday?')) {
      this.getDataMethodsService.deletePublicHoliday(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: PublicHolidayResponse) => {
            if (response.Success) {
              this.loadData();
            } else {
              alert('Error deleting holiday: ' + response.Message);
            }
          },
          error: (error) => {
            console.error('Error deleting holiday:', error);
            alert('Error deleting holiday');
          }
        });
    }
  }

}

// Global functions for button clicks in ag-grid
declare global {
  interface Window {
    deleteHoliday: (id: number) => void;
  }
}

// Set up global functions
if (typeof window !== 'undefined') {
  window.deleteHoliday = (id: number) => {
    // This will be handled by the component instance
    const event = new CustomEvent('deleteHoliday', { detail: { id } });
    document.dispatchEvent(event);
  };
}
