import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridOptions, ColDef } from 'ag-grid-community';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { PublicHolidayResponse, PublicHolidayViewModel, PublicHolidayRegion } from 'src/app/model/publicHoliday.model';
import { PublicHolidayModalComponent } from './publicHolidayModal.component';

@Component({
  selector: 'app-public-holidays',
  template: `
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <h2>Public Holidays Management</h2>
          
          <div class="mb-3">
            <button class="btn btn-primary" (click)="openAddModal()">
              <i class="fas fa-plus"></i> Add Public Holiday
            </button>
          </div>

          <div class="position-relative">
            <div id="excelExport" (click)="excelExport()">
              <img [src]="constants.provideExcelLogo()">
            </div>
            <ag-grid-angular 
              class="ag-theme-balham" 
              [gridOptions]="gridOptions"
              [rowData]="rowData">
            </ag-grid-angular>
          </div>
        </div>
      </div>
    </div>

    <publicHolidayModal #publicHolidayModal></publicHolidayModal>
  `,
  styleUrls: ['../../../styles/components/_agGrid.scss'],
  styles: [`
    .container-fluid {
      padding: 20px;
    }
    
    h2 {
      margin-bottom: 20px;
      color: #333;
    }
    
    .btn {
      margin-right: 10px;
    }
    
    ag-grid-angular {
      height: 450px;
      width: 75%;
      margin: 0 auto;
      display: block;
    }
    
    #excelExport {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      cursor: pointer;
    }
  `]
})
export class PublicHolidaysComponent implements OnInit {
  @ViewChild('publicHolidayModal') publicHolidayModal: PublicHolidayModalComponent;

  private destroy$ = new Subject<void>();
  
  rowData: PublicHolidayViewModel[] = [];
  regions: PublicHolidayRegion[] = [];
  
  gridOptions: GridOptions = {
    columnDefs: this.getColumnDefs(),
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true
    },
    rowSelection: 'single',
    animateRows: true,
    onGridReady: () => {
      this.gridOptions.api?.sizeColumnsToFit();
    }
  };

  constructor(
    public constants: ConstantsService,
    private getDataMethodsService: GetDataMethodsService,
    private modalService: NgbModal
  ) { }

  ngOnInit(): void {
    this.loadData();
    this.loadRegions();

    // Listen for global events from ag-grid buttons
    document.addEventListener('editHoliday', (event: any) => {
      this.editHoliday(event.detail.id);
    });

    document.addEventListener('deleteHoliday', (event: any) => {
      this.deleteHoliday(event.detail.id);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getColumnDefs(): ColDef[] {
    return [
      {
        headerName: 'Date',
        field: 'date',
        width: 150,
        valueFormatter: (params) => {
          if (params.value) {
            return new Date(params.value).toLocaleDateString();
          }
          return '';
        }
      },
      {
        headerName: 'Regions',
        field: 'regionNames',
        width: 300,
        valueFormatter: (params) => {
          if (params.value && Array.isArray(params.value)) {
            return params.value.join(', ');
          }
          return '';
        }
      },
      {
        headerName: 'Actions',
        width: 150,
        cellRenderer: (params) => {
          return `
            <button class="btn btn-sm btn-primary me-2" onclick="window.editHoliday(${params.data.id})">
              <i class="fas fa-edit"></i> Edit
            </button>
            <button class="btn btn-sm btn-danger" onclick="window.deleteHoliday(${params.data.id})">
              <i class="fas fa-trash"></i> Delete
            </button>
          `;
        }
      }
    ];
  }

  loadData(): void {
    this.getDataMethodsService.getPublicHolidays()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (holidays) => {
          this.rowData = holidays.map(holiday => this.mapToViewModel(holiday));
        },
        error: (error) => {
          console.error('Error loading public holidays:', error);
        }
      });
  }

  loadRegions(): void {
    this.getDataMethodsService.getPublicHolidayRegions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (regions) => {
          this.regions = regions;
        },
        error: (error) => {
          console.error('Error loading regions:', error);
        }
      });
  }

  private mapToViewModel(holiday: PublicHolidayResponse): PublicHolidayViewModel {
    const regionNames = holiday.regionIds.map(regionId => {
      const region = this.regions.find(r => r.id === regionId);
      return region ? region.region : `Region ${regionId}`;
    });

    return {
      id: holiday.id,
      date: holiday.date,
      dealerGroupId: holiday.dealerGroupId,
      regionNames: regionNames,
      regionIds: holiday.regionIds
    };
  }

  openAddModal(): void {
    this.publicHolidayModal.openModal(null, this.regions);
    this.publicHolidayModal.holidaySaved
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadData();
      });
  }

  editHoliday(id: number): void {
    const holiday = this.rowData.find(h => h.id === id);
    if (holiday) {
      this.publicHolidayModal.openModal(holiday, this.regions);
      this.publicHolidayModal.holidaySaved
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          this.loadData();
        });
    }
  }

  deleteHoliday(id: number): void {
    if (confirm('Are you sure you want to delete this public holiday?')) {
      this.getDataMethodsService.deletePublicHoliday(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.loadData();
            } else {
              alert('Error deleting holiday: ' + response.message);
            }
          },
          error: (error) => {
            console.error('Error deleting holiday:', error);
            alert('Error deleting holiday');
          }
        });
    }
  }

  excelExport(): void {
    if (this.gridOptions.api) {
      this.gridOptions.api.exportDataAsCsv({
        fileName: 'public-holidays.csv'
      });
    }
  }
}

// Global functions for button clicks in ag-grid
declare global {
  interface Window {
    editHoliday: (id: number) => void;
    deleteHoliday: (id: number) => void;
  }
}

// Set up global functions
if (typeof window !== 'undefined') {
  window.editHoliday = (id: number) => {
    // This will be handled by the component instance
    const event = new CustomEvent('editHoliday', { detail: { id } });
    document.dispatchEvent(event);
  };

  window.deleteHoliday = (id: number) => {
    // This will be handled by the component instance
    const event = new CustomEvent('deleteHoliday', { detail: { id } });
    document.dispatchEvent(event);
  };
}
