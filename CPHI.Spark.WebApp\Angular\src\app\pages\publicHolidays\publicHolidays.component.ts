import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridOptions, ColDef } from 'ag-grid-community';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { PublicHolidayResponse, PublicHolidayViewModel, PublicHolidayRegion } from 'src/app/model/publicHoliday.model';
import { PublicHolidayModalComponent } from './publicHolidayModal.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'app-public-holidays',
  template: `
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div style="margin-left: 20%;">
            <h2>Public Holidays Management</h2>

            <div class="mb-3">
              <button class="btn btn-primary" (click)="openAddModal()">
                <i class="fas fa-plus"></i> Add Public Holiday
              </button>
            </div>
          </div>

          <div class="position-relative">
            <ag-grid-angular 
              class="ag-theme-balham" 
              [gridOptions]="gridOptions"
              [rowData]="rowData">
            </ag-grid-angular>
          </div>
        </div>
      </div>
    </div>

    <publicHolidayModal #publicHolidayModal></publicHolidayModal>
  `,
  styleUrls: ['../../../styles/components/_agGrid.scss'],
  styles: [`
    .container-fluid {
      padding: 20px;
    }
    
    h2 {
      margin-bottom: 20px;
      color: white;
    }
    
    .btn {
      margin-right: 10px;
    }
    
    ag-grid-angular {
      height: 450px;
      width: 75%;
      margin: 0 auto;
      display: block;
    }
    
    #excelExport {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      cursor: pointer;
    }
  `]
})
export class PublicHolidaysComponent implements OnInit {
  @ViewChild('publicHolidayModal') publicHolidayModal: PublicHolidayModalComponent;

  private destroy$ = new Subject<void>();
  
  rowData: PublicHolidayViewModel[] = [];
  regions: any[] = [];
  
  gridOptions: GridOptions = {
    columnDefs: this.getColumnDefs(),
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true
    },
    rowSelection: 'single',
    animateRows: true,
    columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
    },
    onGridReady: () => {
      console.log('Grid ready, rowData length:', this.rowData?.length || 0);
      this.gridOptions.api?.sizeColumnsToFit();
    }
  };

  constructor(
    public constants: ConstantsService,
    private getDataMethodsService: GetDataMethodsService,
    private columnTypeService: ColumnTypesService,
    private modalService: NgbModal
  ) { }

  ngOnInit(): void {
    this.loadRegions();

    // Listen for global events from ag-grid buttons
    document.addEventListener('deleteHoliday', (event: any) => {
      this.deleteHoliday(event.detail.id);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getColumnDefs(): ColDef[] {
    return [
      {
        headerName: 'Date',
        field: 'date',
        width: 150,
        type: 'dateLongYear',
      },
      {
        headerName: 'Regions',
        field: 'regionNames',
        width: 300,
        valueFormatter: (params) => {
          if (params.value && Array.isArray(params.value)) {
            return params.value.join(', ');
          }
          return '';
        }
      },
      {
        headerName: 'Actions',
        width: 150,
        cellRenderer: (params: any) => {
          return `
            <button class="btn btn-sm btn-danger" onclick="window.deleteHoliday(${params.data.id})">
              <i class="fas fa-trash"></i> Delete
            </button>
          `;
        }
      }
    ];
  }

  loadData(): void {
    this.getDataMethodsService.getPublicHolidays()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (holidays) => {
          console.log('Raw holidays data:', holidays);
          const mappedData = holidays.map(holiday => this.mapToViewModel(holiday));
          console.log('Mapped data:', mappedData);
          this.rowData = [...mappedData]; // Create new array reference
          console.log('RowData assigned:', this.rowData);
        },
        error: (error) => {
          console.error('Error loading public holidays:', error);
        }
      });
  }

  loadRegions(): void {
    this.getDataMethodsService.getPublicHolidayRegions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (regions) => {
          console.log('Raw regions data:', regions);
          this.regions = regions;
          // Load data after regions are loaded
          this.loadData();
        },
        error: (error) => {
          console.error('Error loading regions:', error);
        }
      });
  }

  private mapToViewModel(holiday: any): PublicHolidayViewModel {

    // Handle case where RegionIds might be undefined or null
    const regionIds = holiday.RegionIds || [];
    console.log('RegionIds for holiday:', regionIds);
    console.log('Available regions:', this.regions);

    const regionNames = regionIds.map((regionId: number) => {
      const region = this.regions.find((r: any) => r.Id === regionId);
      console.log(`Looking for region ${regionId}, found:`, region);
      return region ? region.Region : `Region ${regionId}`;
    });

    console.log('Mapped region names:', regionNames);

    return {
      id: holiday.Id,
      date: holiday.Date,
      dealerGroupId: holiday.DealerGroupId,
      regionNames: regionNames,
      regionIds: regionIds
    };
  }

  openAddModal(): void {
    this.publicHolidayModal.openModal(this.regions);
    this.publicHolidayModal.holidaySaved
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadData();
      });
  }



  deleteHoliday(id: number): void {
    if (confirm('Are you sure you want to delete this public holiday?')) {
      this.getDataMethodsService.deletePublicHoliday(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: any) => {
            if (response.Success) {
              this.loadData();
            } else {
              alert('Error deleting holiday: ' + response.Message);
            }
          },
          error: (error) => {
            console.error('Error deleting holiday:', error);
            alert('Error deleting holiday');
          }
        });
    }
  }

}

// Global functions for button clicks in ag-grid
declare global {
  interface Window {
    deleteHoliday: (id: number) => void;
  }
}

// Set up global functions
if (typeof window !== 'undefined') {
  window.deleteHoliday = (id: number) => {
    // This will be handled by the component instance
    const event = new CustomEvent('deleteHoliday', { detail: { id } });
    document.dispatchEvent(event);
  };
}
