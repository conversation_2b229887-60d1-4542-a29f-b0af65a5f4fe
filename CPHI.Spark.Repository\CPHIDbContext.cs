﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Aftersales;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.FltOrd;
using CPHI.Spark.Model.Import;
using CPHI.Spark.Model.Tele;
using CPHI.Spark.Model.Scratchcard;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.Model;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Repository
{
   public class CPHIDbContext : IdentityDbContext
   {
      private readonly string _connectionString;  //if we go with constructor approach
      public static IConfiguration Configuration { get; set; }
      public static string envi;

      public CPHIDbContext()
      {

      }

      public CPHIDbContext(string? connString)  //constructor approach
      {
         if (connString != null)
         {
            _connectionString = connString;
         }
      }



      public CPHIDbContext(DbContextOptions<CPHIDbContext> options) : base(options)
      {

      }


      
      public DbSet<CPHI.Spark.Model.ViewModels.ApplicationUser> ApplicationUsers { get; set; }

      public DbSet<Deal> Deals { get; set; }
      //public DbSet<DealDiff> DealDiffs { get; set; }
      public DbSet<Stock> Stocks { get; set; }
      //public DbSet<ModelHeirachyNode> HeirachyNodes { get; set; }
      //public DbSet<NcMargin> NcMargins { get; set; }
      public DbSet<Person> People { get; set; }
      //public DbSet<AuditEntry> AuditEntries { get; set; }
      public DbSet<Site> Sites { get; set; }
      //public DbSet<Location> Locations { get; set; }
      public DbSet<GlobalParam> GlobalParams { get; set; }
      public DbSet<PeopleTarget> PeopleTargets { get; set; }
      //public DbSet<CalendarDay> CalendarDays { get; set; }
      //public DbSet<AfterSalesPerformance> AfterSalesPerformances { get; set; }
      //public DbSet<Notification> Notifications { get; set; }
      //public DbSet<NotificationTemplate> NotificationTemplates { get; set; }
      //public DbSet<NotificationReadReceipt> NotificationReadReceipts { get; set; }
      //public DbSet<SrUser> SrUsers { get; set; }
      //public DbSet<SrConnection> SrConnections { get; set; }
      //public DbSet<SrGroup> SrGroups { get; set; }
      public DbSet<Activity> Activities { get; set; }
      public DbSet<WipLine> WipLines { get; set; }
      public DbSet<VehicleType> VehicleTypes { get; set; }
      public DbSet<OrderType> OrderTypes { get; set; }
      //public DbSet<LoadBatch> LoadBatches { get; set; }
      //public DbSet<AuditChange> AuditChanges { get; set; }
      //public DbSet<OpeningHours> OpeningHours { get; set; }
      public DbSet<StandingValue> StandingValues { get; set; }
      public DbSet<StandingValueType> StandingValueTypes { get; set; }
      public DbSet<Debt> Debts { get; set; }
      public DbSet<Bonus> Bonuses { get; set; }
      //public DbSet<Report> Reports { get; set; }
      public DbSet<Comment> Comments { get; set; }
      //public DbSet<DashboardPage> DashboardPages { get; set; }
      public DbSet<PartsStock> PartsStocks { get; set; }
      public DbSet<Registration> Registrations { get; set; }
      public DbSet<RegChannel> RegChannels { get; set; }
      public DbSet<RegVehClass> RegVehClasses { get; set; }
      public DbSet<Diff> Diffs { get; set; }
      public DbSet<LogMessage> LogMessages { get; set; }
      public DbSet<FinancialLine> FinancialLines { get; set; }
      public DbSet<SarsLine> SarsLines { get; set; }
      public DbSet<RegistrationTarget> RegistrationTargets { get; set; }
      public DbSet<UseLogItem> UseLogItems { get; set; }
      //public DbSet<ErrorLogItem> ErrorLogItems { get; set; }
      public DbSet<SiteSummaryStat> SiteSummaryStats { get; set; }
      //public DbSet<StockTakePhoto> StockTakePhotos { get; set; }
      public DbSet<SalesRole> SalesRoles { get; set; }
      public DbSet<StockRRGSiteItem> StockRRGSiteItems { get; set; }
      public DbSet<ServiceTransaction> ServiceTransactions { get; set; }
      public DbSet<PartsTransaction> PartsTransactions { get; set; }
      public DbSet<TechDailyHoursCount> TechDailyHoursCounts { get; set; }
      public DbSet<EVHCJob> EVHCJobs { get; set; }
      public DbSet<Part> Parts { get; set; }
      public DbSet<VoCScore> VoCScores { get; set; }
      public DbSet<CitNowItem> CitNowItems { get; set; }
      public DbSet<Booking> Bookings { get; set; }
      public DbSet<PartFamilyCode> PartFamilyCodes { get; set; }
      public DbSet<DiffStockPrice> DiffStockPrices { get; set; }
      public DbSet<DailyOrder> DailyOrders { get; set; }
      public DbSet<StockLanding> StockLandings { get; set; }
      public DbSet<DailySiteBookingStat> DailySiteBookingStats { get; set; }
      //public DbSet<BookingTypeSplit> BookingTypeSplits { get; set; }
      public DbSet<SpecLine> SpecLines { get; set; }
      public DbSet<SpecLineDiff> SpecLineDiffs { get; set; }
      public DbSet<SpecCommissionLine> SpecCommissionLines { get; set; }
      public DbSet<SpecCommissionLineDiff> SpecCommissionLineDiffs { get; set; }
      public DbSet<CommissionPayout> CommissionPayouts { get; set; }
      public DbSet<Brand> Brands { get; set; }
      public DbSet<SiteTechCount> SiteTechCounts { get; set; }
      public DbSet<ReportCentre> ReportCentres { get; set; }
      //public DbSet<CommissionLatestPymnt> CommissionLatestPymnts { get; set; }
      public DbSet<Region> Regions { get; set; }
      public DbSet<AvailabilityDefault> AvailabilityDefaults { get; set; }

      public DbSet<AvailabilityOverride> AvailabilityOverrides { get; set; }
      public DbSet<RTSCode> RTSCodes { get; set; }
      public DbSet<PersonPreference> PersonPreferences { get; set; }
      //public DbSet<ServicePlan> ServicePlans { get; set; }
      public DbSet<Translation> Translations { get; set; }
      public DbSet<OemOrder> OemOrders { get; set; }
      public DbSet<LeaverDeal> LeaverDeals { get; set; }

      //new Jan 22
      public DbSet<Department> Departments { get; set; }

      public DbSet<PublicHoliday> PublicHolidays { get; set; }
      public DbSet<PublicHolidayRegion> PublicHolidayRegions { get; set; }
      public DbSet<PublicHolidayRegionMap> PublicHolidayRegionMaps { get; set; }

      public DbSet<ExternalWip> ExternalWips { get; set; }
      public DbSet<NlAccountCode> NlAccountCodes { get; set; }
      public DbSet<NominalLedgerRow> NominalLedgerRows { get; set; }
      public DbSet<Deal> ConfirmedOrderLoads { get; set; }
      public DbSet<Deal> ConfirmedOrders { get; set; }
      public DbSet<OemOrder_Load> OemOrder_Loads { get; set; }
      public DbSet<DealLatest> DealLatests { get; set; }
      public DbSet<SalesActivity> SalesActivities { get; set; }
      public DbSet<GDPR> GDPRs { get; set; }
      public DbSet<ImageRatio> ImageRatios { get; set; }

      //new Jan 18 22
      public DbSet<Registration_Load> Registration_Loads { get; set; }
      public DbSet<OemStockItem> OemStockItems { get; set; }
      public DbSet<OemStockItem_Load> OemStockItem_Loads { get; set; }
      public DbSet<OemOrderTarget> OemOrderTargets { get; set; }

      public DbSet<RatioNewOrder> RatioNewOrders { get; set; }
      public DbSet<RatioNewOrder_Load> RatioNewOrder_Loads { get; set; }
      public DbSet<RatioUsedOrder> RatioUsedOrders { get; set; }
      public DbSet<RatioUsedOrder_Load> RatioUsedOrder_Loads { get; set; }

      public DbSet<DealerGroup> DealerGroups { get; set; }
      public DbSet<MobileAppConfiguration> MobileAppConfigurations { get; set; }
      public DbSet<Alcopa> Alcopas { get; set; }

      public DbSet<CommissionTarget> CommissionTargets { get; set; }
      public DbSet<CommissionAdjustment> CommissionAdjustments { get; set; }
      public DbSet<SiteWorkingDay> SiteWorkingDays { get; set; }

      //new April 22
      public DbSet<StockKeyEvent> StockKeyEvents { get; set; }
      public DbSet<DistrinetOrder> DistrinetOrders { get; set; }
      public DbSet<DistrinetStockItem> DistrinetStockItems { get; set; }
      public DbSet<DistrinetItem> DistrinetItems { get; set; }
      //fcst
      public DbSet<Spark.Model.Fcst.Forecast> FcstForecasts { get; set; }
      public DbSet<Spark.Model.Fcst.Department> FcstDepartments { get; set; }
      public DbSet<Spark.Model.Fcst.ForecastVersion> ForecastVersions { get; set; }
      public DbSet<Spark.Model.Fcst.DoneStatusType> DoneStatusTypes { get; set; }
      public DbSet<Spark.Model.Fcst.ForecastLine> ForecastLines { get; set; }
      public DbSet<Spark.Model.Fcst.AccountDepartment> AccountDepartments { get; set; }
      public DbSet<Spark.Model.Fcst.FinancialLine> FcstFinancialLines { get; set; }
      public DbSet<Spark.Model.Fcst.Account> Accounts { get; set; }


      public DbSet<Spark.Model.Ser.ApprovalState> ApprovalStates { get; set; }
      public DbSet<Spark.Model.Ser.Measure> Measures { get; set; }
      public DbSet<Spark.Model.Ser.Objective> Objectives { get; set; }
      public DbSet<Spark.Model.Ser.Actual> Actuals { get; set; }

      public DbSet<Spark.Model.Ser.Form> Forms { get; set; }
      public DbSet<Spark.Model.Ser.DiscussionSummaryAction> DiscussionSummaryActions { get; set; }


      // The Distrinet Import tables for Spain
      public DbSet<Spark.Model.Import.Distrinet_Order> CPHI_Distrinet_Orders { get; set; }
      public DbSet<Spark.Model.Import.Distrinet_Book> CPHI_Distrinet_Books { get; set; }
      public DbSet<Spark.Model.Import.Distrinet_Stock> CPHI_Distrinet_Stocks { get; set; }

      //the new individual distrinet tables that we merge into
      public DbSet<Spark.Model.Distrinet_Order> Distrinet_Orders { get; set; }
      public DbSet<Spark.Model.Distrinet_Book> Distrinet_Books { get; set; }
      public DbSet<Spark.Model.Distrinet_Stock> Distrinet_Stocks { get; set; }

      public DbSet<Spark.Model.Import.CPHI_Alcopa> CPHI_Alcopa { get; set; }
      //public DbSet<DealLatestKeyEvent> DealLatestKeyEvents { get; set; }
      public DbSet<UpsellType> UpsellTypes { get; set; }
      public DbSet<Upsell> Upsells { get; set; }

      public DbSet<DealfileSentDate> DealfileSentDates { get; set; }
      public DbSet<CustomerVisit> CustomerVisits { get; set; }



      public DbSet<CommissionQualification> CommissionQualifications { get; set; }
      public DbSet<StockRRGSiteDealerMapping> StockRRGSiteDealerMappings { get; set; }

      public DbSet<BusinessUnit> BusinessUnits { get; set; } // SPK-2710 regs


      // For Fleet Orderbook
      public DbSet<FonName> FonNames { get; set; }
      public DbSet<CustomerType> CustomerTypes { get; set; }
      public DbSet<CustomerOrderStatusItem> CustomerOrderStatusItems { get; set; }
      public DbSet<BcaStatusItem> BcaStatusItems { get; set; }
      public DbSet<StockStatusItem> StockStatusItems { get; set; }
      public DbSet<StockCategoryItem> StockCategoryItems { get; set; }
      public DbSet<AlarmDataItem> AlarmDataItems { get; set; }
      public DbSet<InventoryTypeItem> InventoryTypeItems { get; set; }
      public DbSet<GrantDataItem> GrantDataItems { get; set; }
      public DbSet<EbbonStatusItem> EbbonStatusItems { get; set; }
      public DbSet<FleetOrderComment> FleetOrderComments { get; set; }
      public DbSet<BcaStockItem> BcaStockItems { get; set; }
      public DbSet<NissanOrderItem> NissanOrderItems { get; set; }
      public DbSet<RenaultOrderItem> RenaultOrderItems { get; set; }
      public DbSet<EbbonOrderItem> EbbonOrderItems { get; set; }
      public DbSet<OrderTrackingItem> OrderTrackingItems { get; set; }
      public DbSet<FleetOrderTableState> FleetOrderTableStates { get; set; }

      public DbSet<NissanOrderItem_Load> NissanOrderItem_Loads { get; set; }
      public DbSet<RenaultOrderItem_Load> RenaultOrderItem_Loads { get; set; }
      public DbSet<FonName_Load> FonName_Loads { get; set; }
      public DbSet<GrantDataItem_Load> GrantDataItem_Loads { get; set; }
      public DbSet<EbbonOrderItem_Load> EbbonOrderItem_Loads { get; set; }
      public DbSet<BcaStockItem_Load> BcaStockItem_Loads { get; set; }
      public DbSet<Stock_Load> Stock_Loads { get; set; }

      public DbSet<SiteBusinessManager> SiteBusinessManagers { get; set; }
      public DbSet<Revisione> Revisiones { get; set; }
      public DbSet<Ordene> Ordenes { get; set; }

      public DbSet<ExecManagerMapping> ExecManagerMappings { get; set; }

      public DbSet<Revisione_Load> Revisione_Loads { get; set; }
      public DbSet<Ordene_Load> Ordene_Loads { get; set; }

      public DbSet<CPHI_WorkshopHour> CPHI_WorkshopHours { get; set; }

      public DbSet<CPHI_WorkshopHoursPurchased> CPHI_WorkshopHoursPurchased { get; set; }

      public DbSet<CPHI_RepairOrder> CPHI_RepairOrders { get; set; }

      public DbSet<CPHI_Operator> CPHI_Operators { get; set; }

      public DbSet<CPHI_WorkshopAppointment> CPHI_WorkshopAppointments { get; set; }

      public DbSet<WorkshopSite> WorkshopSites { get; set; }
      public DbSet<ReportCentreLog> ReportCentreLogs { get; set; }

      public DbSet<VehicleAwaitingPrep> VehicleAwaitingPreps { get; set; }


      public DbSet<DealRPA> DealRPAs { get; set; }


      //AutoPricing
      public DbSet<VehicleOptOut> VehicleOptOuts { get; set; }
      public DbSet<PriceChangeAutoItem> PriceChangeAutoItems { get; set; }
      public DbSet<PriceChangeManualItem> PriceChangeManualItems { get; set; }
      public DbSet<VehicleAdvertSnapshot> VehicleAdvertSnapshots { get; set; }
      public DbSet<VehicleAdvert> VehicleAdverts { get; set; }
      public DbSet<RetailerSite> RetailerSites { get; set; }

      public DbSet<LeavingPriceItem> LeavingPriceItems { get; set; }
      public DbSet<StrategyVersion> StrategyVersions { get; set; }
      public DbSet<StrategyFactor> StrategyFactors { get; set; }
      public DbSet<StrategyFactorItem> StrategyFactorItems { get; set; }
      public DbSet<StrategyFactorItemVehicleWebsiteRating> StrategyFactorItemVehicleWebsiteRatings { get; set; }
      public DbSet<AutoPriceTableState> AutoPriceTableStates { get; set; }
      public DbSet<VehicleAdvertComment> VehicleAdvertComments { get; set; }
      public DbSet<VehicleAdvertRatingBySite> VehicleAdvertRatingBySites { get; set; }

      public DbSet<VehicleAdvertSpecBuild> VehicleAdvertSpecBuilds { get; set; }
      public DbSet<VehicleAdvertSpecBuildOption> VehicleAdvertSpecBuildOptions { get; set; }
      public DbSet<VehicleOption> VehicleOptions { get; set; }
      public DbSet<VehicleValuationBatch> VehicleValuationBatches { get; set; }
      public DbSet<VehicleValuation> VehicleValuations { get; set; }
      public DbSet<VehicleValuationOption> VehicleValuationOptions { get; set; }
      public DbSet<VehicleValuationRatingBySite> VehicleValuationRatingBySites { get; set; }

      public DbSet<VehicleNumbersToVin> VehicleNumbersToVins { get; set; }

      public DbSet<BroadcastMessage> BroadcastMessages { get; set; }
      public DbSet<BroadcastMessageViewing> BroadcastMessageViewings { get; set; }

      public DbSet<ImportMask> ImportMasks { get; set; }
      public DbSet<LocalBargain> LocalBargains { get; set; }
      public DbSet<Mid> Mids { get; set; }
      public DbSet<VehicleValuationPrepCost> VehicleValuationPrepCosts { get; set; }

      public DbSet<AutoPriceSharedTableState> AutoPriceSharedTableStates { get; set; }
      public DbSet<AutoPriceStandardTableState> AutoPriceStandardTableStates { get; set; }
      public DbSet<PrizeItem> PrizeItems { get; set; }
      public DbSet<PortalOption> PortalOptions { get; set; }
      public DbSet<VehicleAdvertPortalOption> VehicleAdvertPortalOptions { get; set; }
      public DbSet<DealerGroupClaimType> DealerGroupClaimTypes { get; set; }
      public DbSet<StrategyFieldName> StrategyFieldNames { get; set; }
      public DbSet<StrategySelectionRuleSet> StrategySelectionRuleSets { get; set; }
      public DbSet<StrategySelectionRule> StrategySelectionRules { get; set; }
      public DbSet<RetailerSiteStrategySelectionRuleSet> RetailerSiteStrategySelectionRuleSets { get; set; }
      public DbSet<StrategySelectionCriteria> StrategySelectionCriterias { get; set; }
      public DbSet<BuyingCostsSet> BuyingCostsSets { get; set; }
      //public DbSet<VehicleAdvertItem> VehicleAdvertItems { get; set; }

      public DbSet<QualifyingPartEx> QualifyingPartExs { get; set; }

      //Collect all Ads
      public DbSet<AutotraderAdvert> AutotraderAdverts { get; set; }
      public DbSet<AutotraderAdvertFeature> AutotraderAdvertFeatures { get; set; }
      public DbSet<AutotraderAdvertSnapshot> AutotraderAdvertSnapshots { get; set; }
      public DbSet<AutotraderAdvertPriceChange> AutotraderAdvertPriceChanges { get; set; }
      public DbSet<AutotraderFeature> AutotraderFeatures { get; set; }
      public DbSet<AutotraderAdvertLoad> AutotraderAdvertLoads { get; set; }

      // Telesales
      public DbSet<CallDepartment> CallDepartments { get; set; }
      public DbSet<Line> Lines { get; set; }
      public DbSet<Statistic> Statistics { get; set; }

      public DbSet<AutomatedAuditLogItem> AutomatedAuditLogItems { get; set; }
      public DbSet<UserPreference> UserPreferences { get; set; }
      public DbSet<VehicleValuationCompetitorAnalysis> VehicleValuationCompetitorAnalyses { get; set; }
      public DbSet<ExampleItem> ExampleItems { get; set; }

      public DbSet<Deal_JJPremium> Deals_JJPremium { get; set; }

      public DbSet<BcaBranchStockItem> BcaBranchStockItems { get; set; }
      public DbSet<ValuationAuctionFeeItem> ValuationAuctionFeeItems { get; set; }


      // Feb 25 - For Vindis F&I
      public DbSet<FinanceInsuranceTarget> FinanceInsuranceTargets { get; set; }
      public DbSet<FixedCostPrep> FixedCostPreps { get; set; }


      // Mar 25
      public DbSet<BcaBranchStockItem_Load> BcaBranchStockItem_Loads { get; set; }
      public DbSet<AutoPriceSparkTableState> AutoPriceSparkTableStates { get; set; } // Added March 2025


      public DbSet<Deal_Enterprise> Deals_Enterprise { get; set; }
      public DbSet<FixedCostWarranty> FixedCostWarranties { get; set; }

      public DbSet<TradePriceSetting> TradePriceSettings { get; set; }
      public DbSet<RetailCustomerVehicleSnapshot> RetailCustomerVehicleSnapshots { get; set; }
      public DbSet<RetailCustomerVehicleSummaryItem> RetailCustomerVehicleSummaryItems { get; set; }

      public DbSet<AdhocReviewItem> AdhocReviewItems { get; set; }
    

      //protected override void OnConfiguring(DbContextOptionsBuilder options)
      //{

      //    if (!options.IsConfigured)
      //    {

      //        string connStringToUse = _connectionString ?? PickDefaultConnStringForEnvironment();


      //        options.UseSqlServer(connStringToUse, sqlServerOptions => sqlServerOptions.CommandTimeout(300));
      //    }
      //    base.OnConfiguring(options);

      //    //options.UseSqlServer(_connectionString);  alternative if we go with the constructor approach per chatGPT
      //}


      protected override void OnConfiguring(DbContextOptionsBuilder options)
      {
         if (!options.IsConfigured)
         {
            string connStringToUse = _connectionString ?? PickDefaultConnStringForEnvironment();

            options.UseSqlServer(connStringToUse, sqlServerOptions =>
                sqlServerOptions.CommandTimeout(300)
                                .EnableRetryOnFailure(
                                    maxRetryCount: 5, // Number of times to retry before failing
                                    maxRetryDelay: TimeSpan.FromSeconds(10), // Max delay between retries
                                    errorNumbersToAdd: null) // SQL error numbers to consider for retries, null for defaults
            );
         }
         base.OnConfiguring(options);
      }




      private static string PickDefaultConnStringForEnvironment()
      {
         string connStringToUse;

         List<string> environmentsWithoutOwnAppSettings = new List<string>(){
                "loaderProject","windowsAppScraperProject","reporterProject","ftpScraperProject","rpaScraperProject","rpaScraperProject","webScraperProject"
            };

         bool skipAddingEnviAppsettings = environmentsWithoutOwnAppSettings.Contains(envi);

         if (skipAddingEnviAppsettings)
         {
            Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .Build();
         }
         else
         {
            Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{envi}.json")
            .Build();
         }

         connStringToUse = envi == "reporterProject" ?
                     Configuration.GetSection("ConnectionStrings")["SparkDemo"] :
                     Configuration.GetSection("ConnectionStrings")["DefaultConnection"];
         return connStringToUse;
      }







      protected override void OnModelCreating(ModelBuilder modelBuilder)
      {
         base.OnModelCreating(modelBuilder);

         //general property overrides
         modelBuilder.Entity<Person>().Property(p => p.PictureString).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Person>().Property(n => n.Sites).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Person>().Property(n => n.Email).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Stock>().Property(c => c.Colour).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Stock>().Property(n => n.CapNotes).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Stock>().Property(n => n.CapCode).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Stock>().Property(n => n.Options).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Debt>().Property(n => n.Customer).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<LogMessage>().Property(n => n.FailNotes).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Diff>().Property(n => n.OldValue).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Diff>().Property(n => n.NewValue).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<DiffStockPrice>().Property(n => n.OldValue).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<DiffStockPrice>().Property(n => n.NewValue).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Comment>().Property(n => n.Text).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Debt>().Property(n => n.Problem).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Debt>().Property(n => n.ProblemComment).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<StockRRGSiteItem>().Property(x => x.AdvertDescription1).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<StockRRGSiteItem>().Property(x => x.AdvertDescription2).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<StockRRGSiteItem>().Property(x => x.ImageUrls).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<StockRRGSiteItem>().Property(x => x.VideoUrl).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<ServiceTransaction>().Property(x => x.Description).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<RTSCode>().Property(x => x.Description).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<RTSCode>().Property(x => x.Description2).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Booking>().Property(x => x.Notes).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<SpecLine>().Property(x => x.ProductDescription).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<SpecLineDiff>().Property(x => x.Was).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<SpecLineDiff>().Property(x => x.Now).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<WipLine>().Property(x => x.Notes).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<MobileAppConfiguration>().Property(x => x.JSONdefinition).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Bonus>().ToTable("Bonuses");
         modelBuilder.Entity<AvailabilityDefault>().ToTable("AvailabilityDefaults");
         modelBuilder.Entity<AvailabilityOverride>().ToTable("AvailabilityOverrides");
         modelBuilder.Entity<Deal>().ToTable("Deals");
         modelBuilder.Entity<VehicleAwaitingPrep>().ToTable("VehicleAwaitingPreps");
         modelBuilder.Entity<VehicleNumbersToVin>().ToTable("VehicleNumbersToVins");
         modelBuilder.Entity<Mid>().ToTable("Mids");
         modelBuilder.Entity<VehicleValuation>().Property(x => x.CompetitorPricePositions).HasColumnType("nvarchar(2000)");
         modelBuilder.Entity<VehicleValuation>().Property(x => x.CompetitorLink).HasColumnType("nvarchar(1000)");
         modelBuilder.Entity<VehicleAdvert>().Property(x => x.CompetitorLink).HasColumnType("nvarchar(1000)");

         // Constraint to ensure unique combination of Month, ExecId and ManagerId on every row
         modelBuilder.Entity<Spark.Model.ExecManagerMapping>().HasIndex(p => new { p.Month, p.ExecId, p.ManagerId }).IsUnique();

         modelBuilder.Entity<Spark.Model.VehicleNumbersToVin>().HasIndex(p => new { p.Suffix, p.VehicleNumber }).IsUnique();

         // Prevent cascade delete for the x2 FKs (essential for migration)
         modelBuilder.Entity<Spark.Model.ExecManagerMapping>().HasOne(x => x.Exec).WithMany().HasForeignKey(x => x.ExecId).OnDelete(DeleteBehavior.Restrict);
         modelBuilder.Entity<Spark.Model.ExecManagerMapping>().HasOne(x => x.Manager).WithMany().HasForeignKey(x => x.ManagerId).OnDelete(DeleteBehavior.Restrict);
         modelBuilder.Entity<DealRPA>().Property(p => p.ResultMessage).HasColumnType("nvarchar(4000)");

         modelBuilder.Entity<RetailerSite>().Property(x => x.MinimumAutoPricePercentDecrease).HasColumnType("decimal(9, 3)");
         modelBuilder.Entity<RetailerSite>().Property(x => x.MinimumAutoPricePercentIncrease).HasColumnType("decimal(9, 3)");

         // --------------------------------------
         //  to prevent Translation duplicates
         // --------------------------------------
         modelBuilder.Entity<Translation>().Property(t => t.Property).IsRequired();
         modelBuilder.Entity<Translation>().Property(t => t.English).IsRequired();
         modelBuilder.Entity<Translation>().Property(t => t.Spanish).IsRequired();
         modelBuilder.Entity<Translation>().Property(x => x.Property).HasColumnType("nvarchar(200)");
         modelBuilder.Entity<Translation>(entity => { entity.HasIndex(e => e.Property).IsUnique(); });
         modelBuilder.Entity<SalesRole>().HasIndex(e => new { e.PersonId, e.Month, e.Year }).IsUnique();





         // ---------------------------------
         // Salesman Review
         // ---------------------------------
         modelBuilder.Entity<Spark.Model.Ser.Actual>().ToTable("Actuals", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.Objective>().ToTable("Objectives", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.ApprovalState>().ToTable("ApprovalStates", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.Measure>().ToTable("Measures", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.Form>().ToTable("Forms", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.DiscussionSummaryAction>().ToTable("DiscussionSummaryActions", schema: "ser");
         modelBuilder.Entity<Spark.Model.Ser.DiscussionSummaryAction>().Property(x => x.Text).HasColumnType("nvarchar(MAX)");
         modelBuilder.Entity<Spark.Model.Ser.Objective>().Property(entity => entity.Weighting).HasPrecision(18, 3);
         modelBuilder.Entity<Spark.Model.Ser.Form>().HasIndex(p => new { p.Month, p.PersonId }).IsUnique();
         modelBuilder.Entity<Spark.Model.Ser.Actual>().HasIndex(p => new { p.Month, p.PersonId, p.MeasureId }).IsUnique();

         // ---------------------------------
         // Scratchcard
         // ---------------------------------
         modelBuilder.Entity<Spark.Model.Scratchcard.PrizeItem>().ToTable("PrizeItems", schema: "scratchcard");


         // ---------------------------------
         // Distrinet Spain
         // ---------------------------------
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().ToTable("CPHI_Distrinet_Orders", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().ToTable("CPHI_Distrinet_Books", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Stock>().ToTable("CPHI_Distrinet_Stocks", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.CPHI_Alcopa>().ToTable("CPHI_Alcopas", schema: "import");
         modelBuilder.Entity<Revisione_Load>().ToTable("Revisione_Loads", schema: "import");
         modelBuilder.Entity<Ordene_Load>().ToTable("Ordene_Loads", schema: "import");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.Options).HasColumnType("nvarchar(1000)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.SalesmanName).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.TransportLockReason).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.Comments).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.CommentsVehicle).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.CommentsOrder).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.Customer).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<DistrinetOrder>().Property(x => x.PgeoBCV).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.Options).HasColumnType("nvarchar(1000)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.TransportLockReason).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.Comments).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.CommentOrder).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.CommentCarrocero).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.Customer).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Order>().Property(x => x.PgeoBCV).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.Options).HasColumnType("nvarchar(1000)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.TransportLockReason).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.Comments).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.CommentOrder).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.CommentCarrocero).HasColumnType("nvarchar(255)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.Customer).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<Spark.Model.Import.Distrinet_Book>().Property(x => x.PgeoBCV).HasColumnType("nvarchar(100)");
         modelBuilder.Entity<CPHI_Operator>().Property(entity => entity.ProductivePercentage).HasPrecision(17, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.CostAmuntLine).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.CostManualAmountLine).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.DicountAmountLine).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.DtoDeference).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.InvestedTime).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.TotalInvoicedTime).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.SparePartsAmount).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.SubtotalAmountLine).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.TotalAmountLine).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.TotalWithoutTaxes).HasPrecision(34, 4);
         modelBuilder.Entity<CPHI_RepairOrder>().Property(entity => entity.WorkforceAmount).HasPrecision(34, 4);

         // ---------------------------------
         // fcst schema
         // ---------------------------------
         modelBuilder.Entity<Spark.Model.Fcst.ForecastLine>().ToTable("ForecastLines", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.FinancialLine>().ToTable("FinancialLines", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.Forecast>().ToTable("Forecasts", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.ForecastVersion>().ToTable("ForecastVersions", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.Account>().ToTable("Accounts", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.AccountSubtotal>().ToTable("AccountSubtotals", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.AccountAccountSubtotal>().ToTable("AccountAccountSubtotals", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.Actuality>().ToTable("Actualities", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.Department>().ToTable("Departments", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.DoneStatusType>().ToTable("DoneStatusTypes", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.ApprovalState>().ToTable("ApprovalStates", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.Group>().ToTable("Groups", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.AccountDepartment>().ToTable("AccountDepartments", schema: "fcst");
         modelBuilder.Entity<Spark.Model.Fcst.DepartmentVehicleOrderType>().ToTable("DepartmentVehicleOrderTypes", schema: "fcst");




         // ---------------------------------
         // Fleet Orderbook Feature RRG
         // ---------------------------------
         modelBuilder.Entity<FonName>().ToTable("FonNames", schema: "fltord");
         modelBuilder.Entity<CustomerType>().ToTable("CustomerTypes", schema: "fltord");
         modelBuilder.Entity<CustomerOrderStatusItem>().ToTable("CustomerOrderStatusItems", schema: "fltord");
         modelBuilder.Entity<BcaStatusItem>().ToTable("BcaStatusItems", schema: "fltord");
         modelBuilder.Entity<StockStatusItem>().ToTable("StockStatusItems", schema: "fltord");
         modelBuilder.Entity<StockCategoryItem>().ToTable("StockCategoryItems", schema: "fltord");
         modelBuilder.Entity<AlarmDataItem>().ToTable("AlarmDataItems", schema: "fltord");
         modelBuilder.Entity<BcaStockItem>().ToTable("BcaStockItems", schema: "fltord");
         modelBuilder.Entity<NissanOrderItem>().ToTable("NissanOrderItems", schema: "fltord");
         modelBuilder.Entity<RenaultOrderItem>().ToTable("RenaultOrderItems", schema: "fltord");
         modelBuilder.Entity<GrantDataItem>().ToTable("GrantDataItems", schema: "fltord");
         modelBuilder.Entity<OrderTrackingItem>().ToTable("OrderTrackingItems", schema: "fltord");
         modelBuilder.Entity<FleetOrderTableState>().ToTable("FleetOrderTableStates", schema: "fltord");
         modelBuilder.Entity<InventoryTypeItem>().ToTable("InventoryTypeItems", schema: "fltord");
         modelBuilder.Entity<EbbonStatusItem>().ToTable("EbbonStatusItems", schema: "fltord");
         modelBuilder.Entity<EbbonOrderItem>().ToTable("EbbonOrderItems", schema: "fltord");
         modelBuilder.Entity<FleetOrderComment>().ToTable("FleetOrderComments", schema: "fltord");

         modelBuilder.Entity<Spark.Model.Import.NissanOrderItem_Load>().ToTable("NissanOrderItem_Loads", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.RenaultOrderItem_Load>().ToTable("RenaultOrderItem_Loads", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.FonName_Load>().ToTable("FonName_Loads", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.GrantDataItem_Load>().ToTable("GrantDataItem_Loads", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.EbbonOrderItem_Load>().ToTable("EbbonOrderItem_Loads", schema: "import");
         modelBuilder.Entity<Spark.Model.Import.BcaStockItem_Load>().ToTable("BcaStockItem_Loads", schema: "import");
         modelBuilder.Entity<Stock_Load>().ToTable("Stocks", schema: "import");
         modelBuilder.Entity<AutotraderAdvertLoad>().ToTable("AutotraderAdvertLoads", schema: "import");

         modelBuilder.Entity<Spark.Model.FltOrd.NissanOrderItem>(entity => { entity.HasIndex(e => e.VehicleOrderNumber).IsUnique(); });
         modelBuilder.Entity<Spark.Model.FltOrd.RenaultOrderItem>(entity => { entity.HasIndex(e => e.CustomerOrderNo).IsUnique(); });
         modelBuilder.Entity<Spark.Model.FltOrd.InventoryTypeItem>(entity => { entity.HasIndex(e => e.InventoryType).IsUnique(); });
         modelBuilder.Entity<Spark.Model.FltOrd.StockStatusItem>(entity => { entity.HasIndex(e => e.Status).IsUnique(); });
         modelBuilder.Entity<Spark.Model.FltOrd.FleetOrderTableState>().Property(x => x.State).HasColumnType("nvarchar(MAX)");


         // ---------------------------------
         // Telesales tel.
         // ---------------------------------
         modelBuilder.Entity<CallDepartment>().ToTable("CallDepartments", schema: "tel");
         modelBuilder.Entity<Line>().ToTable("Lines", schema: "tel");
         modelBuilder.Entity<Statistic>().ToTable("Statistics", schema: "tel");

         modelBuilder.Entity<VehicleValuation>().Property(x => x.LotNumber).HasColumnType("nvarchar(20)");

         // Autoprice
         modelBuilder.Entity<Deal_JJPremium>().ToTable("Deals_JJPremium", schema: "import");
         modelBuilder.Entity<Deal_Enterprise>().ToTable("Deals_Enterprise", schema: "import");
         modelBuilder.Entity<Deal_CarCo>().ToTable("Deals_CarCo", schema: "import");

         modelBuilder.Entity<FinanceInsuranceTarget>().HasIndex(e => new { e.Site_Id, e.Type, e.Month }).IsUnique();

         // ---------------------------------
         // Ensure these enums are saved as strings
         // ---------------------------------
         modelBuilder.Entity<StrategyFactor>().Property(e => e.Name).HasConversion<string>();
         modelBuilder.Entity<BuyingCostsSet>().Property(e => e.TemplateType).HasConversion<string>();
         modelBuilder.Entity<VehicleValuationBatch>().Property(e => e.TemplateType).HasConversion<string>();
         modelBuilder.Entity<ValuationAuctionFeeItem>().Property(e => e.TemplateType).HasConversion<string>();

         // modelBuilder.Entity<UserPreference>()
         //         .Property(up => up.PreferenceType)
         //         .HasConversion(
         //         v => v.ToString(), // Convert enum to string
         //             v => (PreferenceType)Enum.Parse(typeof(PreferenceType), v) // Convert string to enum
         //);

         //add index to snapshotss
         modelBuilder.Entity<VehicleAdvertSnapshot>().HasIndex(e => e.IsTodayLatestSnapshot);

         modelBuilder.Entity<FixedCostPrep>().HasIndex(e => new { e.DealerGroup_Id, e.Model, e.FromMonths });

      }



      public async Task<int> SaveChangesWithAuditAsync(int userId, CancellationToken cancellationToken = default)
      {
         var auditEntries = new List<AutomatedAuditLogItem>();
         var entryMap = new Dictionary<AutomatedAuditLogItem, EntityEntry>(); // Maps audit entries to their corresponding EntityEntry
         var entries = ChangeTracker.Entries().Where(e => e.State == EntityState.Modified || e.State == EntityState.Added || e.State == EntityState.Deleted);
         foreach (var entry in entries)
         {
            if (entry.Entity is AutomatedAuditLogItem) { continue; }// Skip audit log entries
            foreach (var property in entry.Properties)
            {
               AutomatedAuditLogItem auditEntry = null;
               if (entry.State == EntityState.Added && property.Metadata.Name != "Id")
               {
                  auditEntry = new AutomatedAuditLogItem
                  {
                     OperationType = 'C',
                     TableName = entry.Entity.GetType().Name,
                     ColumnName = property.Metadata.Name,
                     WasValue = null,
                     NowValue = property.CurrentValue?.ToString(),
                     UserId = userId,
                     UpdateDate = DateTime.Now
                  };
                  entryMap[auditEntry] = entry; // Map this audit entry to its EntityEntry
               }
               else if (entry.State == EntityState.Deleted)
               {
                  auditEntries.Add(new AutomatedAuditLogItem
                  {
                     OperationType = 'D',
                     TableName = entry.Entity.GetType().Name,
                     TablePKId = (int)entry.Property("Id").CurrentValue,
                     ColumnName = property.Metadata.Name,
                     WasValue = property.OriginalValue?.ToString(),
                     NowValue = null,
                     UserId = userId,
                     UpdateDate = DateTime.Now
                  });
               }
               else if (entry.State == EntityState.Modified && property.IsModified)
               {
                  auditEntries.Add(new AutomatedAuditLogItem
                  {
                     OperationType = 'U',
                     TableName = entry.Entity.GetType().Name,
                     TablePKId = (int)entry.Property("Id").CurrentValue,
                     ColumnName = property.Metadata.Name,
                     WasValue = property.OriginalValue?.ToString(),
                     NowValue = property.CurrentValue?.ToString(),
                     UserId = userId,
                     UpdateDate = DateTime.Now
                  });
               }
               if (auditEntry != null) { auditEntries.Add(auditEntry); } // Only add if not null
            }
         }

         var result = await base.SaveChangesAsync(cancellationToken);

         // Update TablePKId for newly created entities
         foreach (var pair in entryMap)
         {
            pair.Key.TablePKId = (int)pair.Value.Property("Id").CurrentValue; // Correctly retrieve the ID from the tracked EntityEntry
         }

         // Save audit entries
         if (auditEntries.Any())
         {
            this.AutomatedAuditLogItems.AddRange(auditEntries);
            await base.SaveChangesAsync(cancellationToken);
         }
         return result;
      }



   }
}

