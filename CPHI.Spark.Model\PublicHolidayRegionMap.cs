﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
    public class PublicHolidayRegionMap
    {
        [Key]
        public int Id { get; set; }

        public int PublicHolidayId { get; set; }
        [ForeignKey("PublicHolidayId")]
        public PublicHoliday PublicHoliday { get; set; }

        public int PublicHolidayRegionId { get; set; }
        [ForeignKey("PublicHolidayRegionId")]
        public PublicHolidayRegion PublicHolidayRegion { get; set; }
    }
}

