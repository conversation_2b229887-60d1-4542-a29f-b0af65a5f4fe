using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Ser;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Dapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Microsoft.Extensions.Configuration;
using MoreLinq;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IAutoPriceDealerOnboardDataAccess
   {
      Task AddNewSitesForExistingDealergroup(NewDealerGroupParams parms);
      Task CreateNewDealerGroup(NewDealerGroupParams parms, UserManager<ApplicationUser> userManager);
   }

   public class AutoPriceDealerOnboardDataAccess : IAutoPriceDealerOnboardDataAccess
   {
      private readonly string _connectionString;



      public AutoPriceDealerOnboardDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }






      public async Task CreateNewDealerGroup(NewDealerGroupParams parms, UserManager<ApplicationUser> userManager)
      {

         string userNameWithGroupName = $"rp.{parms.NewGroupShortName}@cphi.co.uk";
         var applicationUser = new ApplicationUser()
         {
            UserName = userNameWithGroupName,
            Email = userNameWithGroupName,
            LinkedPersonId = 0,
            EmailConfirmed = true
         };


         using (var db = new CPHIDbContext(_connectionString))
         {
            //db.Database.GetDbConnection().ConnectionString = _connectionString;


            try
            {
               var strategy = db.Database.CreateExecutionStrategy();

               await strategy.ExecuteAsync(async () =>
               {
                  // Start a transaction
                  using (var transaction = await db.Database.BeginTransactionAsync())
                  {

                     //-----------------------
                     //Do the stuff
                     //-----------------------
                     Tuple<Person, List<RetailerSite>, DealerGroup> newSetupItems = await SetupPersonAndSites(parms, db);
                     int newUserId = newSetupItems.Item1.Id;
                     applicationUser.LinkedPersonId = newUserId;



                     //should be all setup.   now need to create an aspnetuser
                     var result = await userManager.CreateAsync(applicationUser, parms.Password);

                     //now need to assign a role
                     var roleResult = await userManager.AddToRoleAsync(applicationUser, "SYSTEM ADMINISTRATOR");


                     //and add claims
                     //var baseUser = userManager.FindByEmailAsync("<EMAIL>").Result;
                     //IList<Claim> baseClaims = userManager.GetClaimsAsync(baseUser).Result;
                     //await userManager.AddClaimsAsync(applicationUser, baseClaims);

                     //Create standard claims
                     string claimsSQL = $"INSERT INTO AspNetUserClaims (UserId,ClaimType,ClaimValue) " +
                          $"VALUES " +
                          $"('{applicationUser.Id}','canActionStockPrices','true')," +
                          $"('{applicationUser.Id}','canReviewStockPrices','true')," +
                          $"('{applicationUser.Id}','seeUserMaintenance','true')," +
                          $"('{applicationUser.Id}','seeStockPricing','true')," +
                          $"('{applicationUser.Id}','canSetStandardReports','true')," +
                          $"('{applicationUser.Id}','canEditPricingStrategy','true')" +
                          $"";
                     await db.Database.ExecuteSqlRawAsync(claimsSQL);


                     if (parms.CreateStandardStrategy)
                     {
                        await CreateStrategy(newUserId, db, newSetupItems);
                     }
                     await AddDealerGroupClaimTypes(db, newSetupItems);
                     await db.SaveChangesAsync();

                     //-----------------------
                     //Change the password hash
                     //-----------------------
                     string hash = "AQAAAAIAAYagAAAAEGA/k7jqI9rCB80b+Rhl0zdxCIb0ddfORivqNfZZCIlIPRSGlPFvg61rNRxUB5mVcQ=="; //our usual password
                     string updatePasswordSQL = $"UPDATE AspNetUsers Set PasswordHash = '{hash}' WHERE Email = '{userNameWithGroupName}'";
                     await db.Database.ExecuteSqlRawAsync(updatePasswordSQL);

                     //-----------------------
                     //Switch the email and username to be standard me
                     //-----------------------
                     string newEmail = "<EMAIL>";
                     string updateEmailSQL = $"UPDATE AspNetUsers SET Email = '{newEmail}', UserName = '{newEmail}', NormalizedUserName = '{newEmail.ToUpper()}', NormalizedEmail = '{newEmail.ToUpper()}' WHERE Email = '{userNameWithGroupName}'";
                     await db.Database.ExecuteSqlRawAsync(updateEmailSQL);


                     //--------------------------
                     //New - create table states
                     //--------------------------
                     var existingSparkStates = await db.AutoPriceSparkTableStates.Where(x=>x.DealerGroupId == 26).ToListAsync();  //use LSH ones
                     var existingStateIds = existingSparkStates.Select(x => x.AutoPriceTableStateId).ToList();
                     var existingStates = await db.AutoPriceTableStates.Where(x=> existingStateIds.Contains(x.Id)).ToListAsync();  
                     List<AutoPriceTableState> newStates = new List<AutoPriceTableState>();
                     foreach (var state in existingStates)
                     {
                        var newState = new AutoPriceTableState(state);
                        newState.Person_Id = newUserId;
                        newStates.Add(newState);
                     }
                     await db.AutoPriceTableStates.AddRangeAsync(newStates);
                     await db.SaveChangesAsync();

                     //Add SparkTableStates based on these
                     List<AutoPriceSparkTableState> newSparkStates = new List<AutoPriceSparkTableState>();
                     int dgId = newSetupItems.Item3.Id;
                     foreach (var state in newStates)
                     {
                        var newSparkState = new AutoPriceSparkTableState(state, newUserId, dgId);
                        newSparkState.DealerGroupId = newSetupItems.Item3.Id;
                        newSparkStates.Add(newSparkState);
                     }
                     await db.AutoPriceSparkTableStates.AddRangeAsync(newSparkStates);
                     await db.SaveChangesAsync();



                     //-----------------------
                     // Commit the transaction
                     //-----------------------
                     await transaction.CommitAsync();
                  }
               });

            }
            catch (Exception ex)
            {
               // Rollback transaction if any operation fails
               //await transaction.RollbackAsync();
               await userManager.DeleteAsync(applicationUser);
               // Handle exception (e.g., log the error, throw it, etc.)
               throw new InvalidOperationException("An error occurred during the transaction.", ex);
            }
         }
      }


      private static async Task CreateStrategy(int newPersonId, CPHIDbContext db, Tuple<Person, List<RetailerSite>, DealerGroup> setupItems)
      {
         //Create Strategy Version
         var newVersion = new StrategyVersion()
         {
            CreatedBy_Id = newPersonId,
            LastUpdatedBy_Id = newPersonId,
            LastUpdatedDate = DateTime.UtcNow,
            Name = "First Policy",
            Comment = null
         };
         await db.StrategyVersions.AddAsync(newVersion);
         await db.SaveChangesAsync();

         //Create factors
         var factorsToCopy = db.StrategyFactors.Where(x => x.StrategyVersion_Id == 9);
         var newFactors = new List<StrategyFactor>();
         foreach (var factorToCopy in factorsToCopy)
         {
            newFactors.Add(new StrategyFactor(factorToCopy, newVersion.Id));
         }
         await db.StrategyFactors.AddRangeAsync(newFactors);
         await db.SaveChangesAsync();

         //Create factor items
         var factorItemsToCopy = await db.StrategyFactorItems.Where(x => x.StrategyFactor.StrategyVersion_Id == 9).Include(x => x.StrategyFactor).ToListAsync();
         var newFactorItems = new List<StrategyFactorItem>();
         foreach (var factorItemToCopy in factorItemsToCopy)
         {
            var factor = newFactors.First(x => x.Name == factorItemToCopy.StrategyFactor.Name);
            newFactorItems.Add(new StrategyFactorItem(factorItemToCopy, factor.Id));
         }
         await db.StrategyFactorItems.AddRangeAsync(newFactorItems);
         await db.SaveChangesAsync();



         //New - now also create the new strategy selection rule set stuff

         StrategySelectionRuleSet newRuleSet = new StrategySelectionRuleSet()
         {
            CreatedBy_Id = newPersonId,
            LastUpdatedBy_Id = newPersonId,
            DefaultStrategyVersion_Id = newVersion.Id,
            CreatedDate = DateTime.UtcNow,
            LastUpdatedDate = DateTime.UtcNow,
            Comment = "Created from First Strategy",
            Name = "First Strategy"
         };
         db.StrategySelectionRuleSets.Add(newRuleSet);
         await db.SaveChangesAsync();
         foreach (var retailerSite in setupItems.Item2)
         {
            retailerSite.StrategySelectionRuleSet_Id = newRuleSet.Id;
            retailerSite.BuyingStrategySelectionRuleSet_Id = newRuleSet.Id;
         }


         await db.SaveChangesAsync();
      }

      public async Task AddNewSitesForExistingDealergroup(NewDealerGroupParams parms)
      {

         using (var db = new CPHIDbContext(_connectionString))
         {

            try
            {

               var strategy = db.Database.CreateExecutionStrategy();

               await strategy.ExecuteAsync(async () =>
               {
                  // Start a transaction
                  using (var transaction = await db.Database.BeginTransactionAsync())
                  {

                     var existingDg = db.DealerGroups.FirstOrDefault(x => x.Name == parms.NewDealerGroupName);
                     if (existingDg == null)
                        return;
                     var allRegions = await AddNewRegionsForDealerGroupAsRequired(parms, existingDg.Id, db);
                     var newSites = await InsertSites(parms, db, existingDg, allRegions);
                     var newRetailerSites = await InsertRetailerSites(parms, db, existingDg, allRegions, newSites);

                     //-----------------------
                     // Commit the transaction
                     //-----------------------
                     await transaction.CommitAsync();
                  }
               });
            }


            catch (Exception ex)
            {
               // Rollback transaction if any operation fails
               //await transaction.RollbackAsync();
               // Handle exception (e.g., log the error, throw it, etc.)
               throw new InvalidOperationException("An error occurred during the transaction.", ex);
            }

         }

         //should be done.
      }

      private static async Task<Tuple<Person, List<RetailerSite>, DealerGroup>> SetupPersonAndSites(NewDealerGroupParams parms, CPHIDbContext db)
      {
         //initially setup person against LMC (it's a bit chicken and egg)
         var lmcDgId = (await db.DealerGroups.FirstAsync(x => x.Name == "LMC")).Id;
         var lmcSiteId = (await db.Sites.Where(x => x.DealerGroup_Id == lmcDgId).FirstAsync()).Id;
         var lmcRetailerSiteId = (await db.RetailerSites.Where(x => x.DealerGroup_Id == lmcDgId).FirstAsync()).Id;
         Person newPerson = await InsertPerson(db, lmcDgId, lmcSiteId, lmcRetailerSiteId);

         //then setup the DG, regions, sites
         DealerGroup newDealerGroup = await InsertDealerGroup(parms, db, newPerson);
         List<Region> newRegions = await InsertRegions(parms, db);
         List<Site> newSites = await InsertSites(parms, db, newDealerGroup, newRegions);
         List<RetailerSite> newRetailerSites = await InsertRetailerSites(parms, db, newDealerGroup, newRegions, newSites);

         //insert global params
         await InsertGlobalParams(newDealerGroup.Id, parms.IsMultiSiteGroup, db);

         //update person to be against the new DG
         newPerson.Sites = string.Join(",", newSites.Select(x => x.Id));
         newPerson.CurrentSite_Id = newSites.First().Id;
         newPerson.CurrentRetailerSite_Id = newRetailerSites.First().Id;
         await db.SaveChangesAsync();

         return new Tuple<Person, List<RetailerSite>, DealerGroup>(newPerson, newRetailerSites, newDealerGroup);
      }

      private static async Task AddNewSites(NewDealerGroupParams parms, CPHIDbContext db)
      {

      }

      private static async Task<List<RetailerSite>> InsertRetailerSites(NewDealerGroupParams parms, CPHIDbContext db, DealerGroup newDealerGroup, List<Region> newRegions, List<Site> newSites)
      {
         int retailerSiteIndex = 1;
         List<RetailerSite> newRetailerSites = new List<RetailerSite> { };
         foreach (var region in parms.Regions)
         {
            var regionId = newRegions.First(x => x.Description == region.RegionName).Id;
            region.Sites.ForEach(site =>
            {

               newRetailerSites.Add(new RetailerSite()
               {
                  Site_Id = newSites.First(x => x.Description == site.SiteName).Id,
                  Name = site.SiteName,
                  RetailerId = site.DealerNumber,
                  IsActive = true,
                  FakeName = site.SiteName,
                  Postcode = site.Postcode,
                  Makes = site.Makes,
                  DealerGroup_Id = newDealerGroup.Id,


                  OverUnderIsPercent = true,
                  OverUnderThreshold = 0.02M,
                  VeryThresholdIsPercent = true,
                  VeryThreshold = 0.05M,
                  StrategySelectionRuleSet_Id = 15, //will be replaced later in the setup process

                  //new stuff to consider
                  WhenToActionChangesEachDay = 16,
                  MaximumOptOutDays = 28,
                  MinimumAutoPriceDecrease = -50,
                  MinimumAutoPriceIncrease = 50,
                  LocalBargainThreshold = 500,
                  LocationMovePoundPerMile = 0.2M,
                  LocationMoveFixedCostPerMove = 50,
                  CompetitorPlateRange = 1,
                  LocalBargainsSearchRadius = 50,
                  LocalBargainsMinRetailRating = 0,
                  DaysMeasure = "DaysListed",
                  TargetMargin = 2000,
                  IncludeUnPublishedAds = false,
                  UpdatePricesAutomatically = false,
                  UpdatePricesMon = false,
                  UpdatePricesTue = false,
                  UpdatePricesWed = false,
                  UpdatePricesThu = false,
                  UpdatePricesFri = false,
                  UpdatePricesPubHolidays = false,
                  UpdatePricesSat = false,
                  UpdatePricesSun = false,
                  MinimumAutoPricePercentDecrease = 0,
                  MinimumAutoPricePercentIncrease = 0,
                  SortPriceChangesReportBy = null,
                  AdminFee = 0,
                  LifecycleStatusDefaults = "FORECOURT,SALE_IN_PROGRESS",
                  RetailerType = site.RetailerType,

                  AllowTestStrategy = true
               });


            });

            retailerSiteIndex++;
         }
         db.RetailerSites.AddRange(newRetailerSites);
         await db.SaveChangesAsync();
         return newRetailerSites;
      }

      private static async Task<Person> InsertPerson(CPHIDbContext db, int lmcDgId, int lmcSiteId, int lmcRetailerSiteId)
      {
         Person newPerson = new Person()
         {
            Name = "Richard Procter",
            Email = "<EMAIL>",
            KnownAs = "Rich P",
            CurrentSite_Id = lmcSiteId,
            CurrentRetailerSite_Id = lmcRetailerSiteId,
            JobTitle = "System Administrator",
            JobRole = "System Administrator",
            Sites = $"{lmcSiteId}",
            DealerGroup_Id = lmcDgId,
            LastUpdateDate = DateTime.Now,
         };
         db.People.Add(newPerson);
         await db.SaveChangesAsync();
         return newPerson;
      }

      private static async Task<DealerGroup> InsertDealerGroup(NewDealerGroupParams parms, CPHIDbContext db, Person newPerson)
      {
         var newDealerGroup = new DealerGroup()
         {
            Name = parms.NewDealerGroupName,
            DealerGroupId = 0,
            SystemUser_Id = newPerson.Id
         };
         db.DealerGroups.Add(newDealerGroup);
         await db.SaveChangesAsync();
         newDealerGroup.DealerGroupId = newDealerGroup.Id;  //have to update after has been saved
         await db.SaveChangesAsync();


         //update person to be at new DG
         newPerson.DealerGroup_Id = newDealerGroup.Id;
         await db.SaveChangesAsync();
         return newDealerGroup;
      }


      private static async Task InsertGlobalParams(int dealerGroupId, bool valueVehicleAtAllSites, CPHIDbContext db)
      {
         db.GlobalParams.Add(new GlobalParam()
         {
            DateFrom = new DateTime(2024, 1, 1),
            Value = 0,
            Description = "autotraderAdvertsUpdateDate",
            DealerGroup_Id = dealerGroupId,
         });

         db.GlobalParams.Add(new GlobalParam()
         {
            DateFrom = new DateTime(2024, 1, 1),
            Value = 0,
            Description = "stocksUpdateDate",
            DealerGroup_Id = dealerGroupId,
         });

         db.GlobalParams.Add(new GlobalParam()
         {
            DateFrom = new DateTime(2024, 1, 1),
            Value = 0,
            Description = "ValueVehicleAtAllSites",
            TextValue = valueVehicleAtAllSites ? "True" : "False",
            DealerGroup_Id = dealerGroupId,
         });

         await db.SaveChangesAsync();
      }

      private static async Task<List<Region>> InsertRegions(NewDealerGroupParams parms, CPHIDbContext db)
      {
         int regionIndex = 1;
         List<Region> newRegions = new List<Region>() { };
         foreach (var region in parms.Regions)
         {
            newRegions.Add(new Region()
            {
               Order = regionIndex,
               Description = region.RegionName,
            });
            regionIndex++;
         }
         db.Regions.AddRange(newRegions);
         await db.SaveChangesAsync();
         return newRegions;
      }

      private static async Task<List<Region>> AddNewRegionsForDealerGroupAsRequired(NewDealerGroupParams parms, int dealerGroupId, CPHIDbContext db)
      {
         // Retrieve existing regions for the dealer group
         var existingRegions = await db.Regions
             .Where(x => x.Sites.First().DealerGroup_Id == dealerGroupId)
             .ToListAsync();

         int regionIndex = existingRegions.Count + 1;

         // Iterate through incoming regions to identify and add only new regions
         foreach (var incomingRegion in parms.Regions)
         {
            // Check if the incoming region already exists by description
            var existingRegion = existingRegions.FirstOrDefault(r => r.Description == incomingRegion.RegionName);

            if (existingRegion == null)
            {
               // If the region is not in existingRegions, add it as a new region
               var newRegion = new Region
               {
                  Order = regionIndex,
                  Description = incomingRegion.RegionName,
               };

               db.Regions.Add(newRegion);
               regionIndex++;
            }
         }

         // Save changes to the database to persist new regions
         await db.SaveChangesAsync();

         // Return all regions for the dealer group, including new and existing ones
         return await db.Regions
             .Where(x => x.Sites.First().DealerGroup_Id == dealerGroupId)
             .OrderBy(r => r.Order)
             .ToListAsync();
      }

      private static async Task<List<Site>> InsertSites(NewDealerGroupParams parms, CPHIDbContext db, DealerGroup newDealerGroup, List<Region> newRegions)
      {
         int siteIndex = 1;
         List<Site> newSites = new List<Site> { };
         foreach (var region in parms.Regions)
         {
            var regionId = newRegions.First(x => x.Description == region.RegionName).Id;
            region.Sites.ForEach(site =>
            {

               newSites.Add(new Site()
               {
                  Region_Id = regionId,
                  SortOrder = siteIndex,
                  Description = site.SiteName,
                  DescShort = site.SiteName,
                  IsRetail = true,
                  IsActive = true,
                  IsSales = true,
                  DealerNumber = site.DealerNumber,
                  DealerGroup_Id = newDealerGroup.Id,
                  WebsiteId = site.DealerNumber,
                  GeoX = site.GeoX.ToString(),
                  GeoY = site.GeoY.ToString()
               });
            });

            siteIndex++;
         }
         db.Sites.AddRange(newSites);
         await db.SaveChangesAsync();
         return newSites;
      }


      private static async Task AddDealerGroupClaimTypes(CPHIDbContext db, Tuple<Person, List<RetailerSite>, DealerGroup> newSetupItems)
      {
         DealerGroup newDG = newSetupItems.Item3;
         List<DealerGroupClaimType> newClaimTypes = new List<DealerGroupClaimType>()
                {
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "canActionStockPrices",ClaimValues = "true,false"},
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "canReviewStockPrices",ClaimValues = "true,false"},
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "seeStockPricing",ClaimValues = "true,false"},
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "seeUserMaintenance",ClaimValues = "true,false"},
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "canSetStandardReports",ClaimValues = "true,false"},
                    new DealerGroupClaimType{DealerGroupId = newDG.Id, ClaimType = "canEditPricingStrategy",ClaimValues = "true,false"},
                };
         await db.DealerGroupClaimTypes.AddRangeAsync(newClaimTypes);
         await db.SaveChangesAsync();
      }

      public async Task TestSetupClaims(UserManager<ApplicationUser> userManager)
      {
         // Retrieve all users with the given email
         var users = await userManager.Users.Where(u => u.Email == "<EMAIL>").ToListAsync();

         // Find the user with the specific linkedPersonId
         var applicationUser = users.FirstOrDefault(u => u.LinkedPersonId == 1140);



         if (applicationUser == null)
         {
            // Handle case where user is not found
            throw new Exception("User not found.");
         }


         using (var db = new CPHIDbContext(_connectionString))
         {
            string sql = $"INSERT INTO AspNetUserClaims (UserId,ClaimType,ClaimValue) VALUES ('{applicationUser.Id}','foo','bar')";
            await db.Database.ExecuteSqlRawAsync(sql);
         }

         { }
      }
   }
}

