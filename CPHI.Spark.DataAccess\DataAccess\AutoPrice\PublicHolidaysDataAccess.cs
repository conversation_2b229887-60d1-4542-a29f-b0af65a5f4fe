using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.EntityFrameworkCore;
using Dapper;
using System.Data;
using CPHI.Spark.Model.Services;
using System.Drawing;
using System.Linq;

namespace CPHI.Spark.DataAccess.DataAccess.AutoPrice
{
    public interface IPublicHolidaysDataAccess
    {
        Task<bool> TodayIsABankHoliday(int regionId);
        Task AddPublicHolidayAsync(DateTime date, string regionName, DealerGroupName dealerGroup);

        Task RemovePublicHolidayAsync(int id);
        Task<List<PublicHolidayResponse>> GetPublicHolidays();
        Task<List<PublicHolidayRegion>> GetRegions();
    }

    public class PublicHolidaysDataAccess : IPublicHolidaysDataAccess
    {


        private readonly string _connectionString;

        public PublicHolidaysDataAccess(string connectionString)
        {
            this._connectionString = connectionString;
        }


      public async Task<bool> TodayIsABankHoliday(int regionId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var today = DateTime.Now.Date;

            var todayHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date == today);

            // No holidays at any region
            if (todayHoliday == null)
               return false;

            return await db.PublicHolidayRegionMaps
                .AnyAsync(x => x.PublicHolidayId == todayHoliday.Id && x.PublicHolidayRegionId == regionId);
         }
      }

      public async Task<Dictionary<int, bool>> GetBankHolidayDictionaryForRetailerSites(int dealerGroupId, DateTime date)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            // Get today's public holiday for the dealer group
            var publicHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date.Date == date.Date && x.DealerGroup_Id == dealerGroupId);

            // If no holiday today, return all false
            if (publicHoliday == null)
            {
               return await db.RetailerSites
                   .Where(rs => rs.DealerGroup_Id == dealerGroupId)
                   .ToDictionaryAsync(rs => rs.Id, rs => false);
            }

            // Get regionIds that are observing today's holiday
            var activeRegionIds = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == publicHoliday.Id)
                .Select(x => x.PublicHolidayRegionId)
                .ToListAsync();

            // Join RetailerSites -> Sites and determine if their region is in activeRegionIds
            var siteRegionMap = await (
                from rs in db.RetailerSites
                join s in db.Sites on rs.Site_Id equals s.Id
                where rs.DealerGroup_Id == dealerGroupId
                select new
                {
                   RetailerSiteId = rs.Id,
                   IsBankHoliday = activeRegionIds.Contains((int) s.PublicHolidayRegionId)
                }
            ).ToListAsync();

            return siteRegionMap.ToDictionary(x => x.RetailerSiteId, x => x.IsBankHoliday);
         }
      }

      public async Task AddPublicHolidayAsync(DateTime date, string regionName, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find or create the region
            var region = await db.PublicHolidayRegions
                .FirstOrDefaultAsync(x => x.Region == regionName);

            if (region == null)
            {
               region = new PublicHolidayRegion { Region = regionName };
               db.PublicHolidayRegions.Add(region);
               await db.SaveChangesAsync();
            }

            // Get all dealer groups
            var allDealerGroups = Enum.GetValues(typeof(DealerGroupName)).Cast<DealerGroupName>();

            foreach (var dealerGroupId in allDealerGroups)
            {
               // Check if holiday already exists for this date and dealer group
               var existingHoliday = await db.PublicHolidays
                   .FirstOrDefaultAsync(x => x.Date.Date == date.Date && x.DealerGroup_Id == (int)dealerGroupId);

               if (existingHoliday == null)
               {
                  // Create new public holiday for this dealer group
                  var publicHoliday = new PublicHoliday
                  {
                     Date = date.Date,
                     DealerGroup_Id = (int)dealerGroupId
                  };

                  db.PublicHolidays.Add(publicHoliday);
                  await db.SaveChangesAsync();

                  // Create the region mapping
                  var regionMap = new PublicHolidayRegionMap
                  {
                     PublicHolidayId = publicHoliday.Id,
                     PublicHolidayRegionId = region.Id
                  };

                  db.PublicHolidayRegionMaps.Add(regionMap);
                  await db.SaveChangesAsync();
               }
               else
               {
                  // Holiday exists for this dealer group, check if this region is already mapped
                  var existingMapping = await db.PublicHolidayRegionMaps
                      .FirstOrDefaultAsync(x => x.PublicHolidayId == existingHoliday.Id && x.PublicHolidayRegionId == region.Id);

                  if (existingMapping == null)
                  {
                     // Add the region mapping to existing holiday
                     var regionMap = new PublicHolidayRegionMap
                     {
                        PublicHolidayId = existingHoliday.Id,
                        PublicHolidayRegionId = region.Id
                     };

                     db.PublicHolidayRegionMaps.Add(regionMap);
                     await db.SaveChangesAsync();
                  }
               }
            }
         }
      }


      public async Task EditPublicHolidayAsync(int id, DateTime date, string regionName)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the existing public holiday
            var existingHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Id == id);

            if (existingHoliday == null)
            {
               throw new ArgumentException("Public holiday not found");
            }

            // Update the public holiday date
            existingHoliday.Date = date.Date;

            // Find or create the region
            var region = await db.PublicHolidayRegions
                .FirstOrDefaultAsync(x => x.Region == regionName);

            if (region == null)
            {
               region = new PublicHolidayRegion { Region = regionName };
               db.PublicHolidayRegions.Add(region);
               await db.SaveChangesAsync();
            }

            // Remove existing region mappings for this holiday
            var existingMaps = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == id)
                .ToListAsync();
            db.PublicHolidayRegionMaps.RemoveRange(existingMaps);

            // Create new region mapping
            var regionMap = new PublicHolidayRegionMap
            {
               PublicHolidayId = id,
               PublicHolidayRegionId = region.Id
            };

            db.PublicHolidayRegionMaps.Add(regionMap);
            await db.SaveChangesAsync();
         }
      }

      public async Task RemovePublicHolidayAsync(int id)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the existing public holiday
            var existingHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Id == id);

            if (existingHoliday == null)
            {
               throw new ArgumentException("Public holiday not found");
            }

            // Get the date of this holiday to find all holidays on the same date across all dealer groups
            var holidayDate = existingHoliday.Date.Date;

            // Find all holidays on the same date across all dealer groups
            var allHolidaysOnDate = await db.PublicHolidays
                .Where(x => x.Date.Date == holidayDate)
                .ToListAsync();

            // Remove all region mappings for all holidays on this date
            var holidayIds = allHolidaysOnDate.Select(x => x.Id).ToList();
            var existingMaps = await db.PublicHolidayRegionMaps
                .Where(x => holidayIds.Contains(x.PublicHolidayId))
                .ToListAsync();
            db.PublicHolidayRegionMaps.RemoveRange(existingMaps);

            // Remove all public holidays on this date
            db.PublicHolidays.RemoveRange(allHolidaysOnDate);
            await db.SaveChangesAsync();
         }
      }

      public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
      {
         try
         {
            using (var db = new CPHIDbContext(_connectionString))
            {
               var holidayData = await (from map in db.PublicHolidayRegionMaps
                                       join holiday in db.PublicHolidays on map.PublicHolidayId equals holiday.Id
                                       join region in db.PublicHolidayRegions on map.PublicHolidayRegionId equals region.Id
                                       select new
                                       {
                                          HolidayId = holiday.Id,
                                          Date = holiday.Date,
                                          DealerGroupId = holiday.DealerGroup_Id,
                                          RegionId = region.Id
                                       }).ToListAsync();

               var result = new List<PublicHolidayResponse>();

               // Group by holiday to combine region IDs
               var groupedHolidays = holidayData.GroupBy(x => new { x.HolidayId, x.Date, x.DealerGroupId });

               foreach (var group in groupedHolidays)
               {
                  result.Add(new PublicHolidayResponse
                  {
                     Id = group.Key.HolidayId,
                     Date = group.Key.Date,
                     DealerGroupId = group.Key.DealerGroupId,
                     RegionIds = group.Select(x => x.RegionId).ToList(),
                     Success = true,
                     Message = "Success"
                  });
               }

               return result;
            }
         }
         catch (Exception ex)
         {
            return new List<PublicHolidayResponse>
            {
               new PublicHolidayResponse
               {
                  Success = false,
                  Message = $"Error retrieving public holidays: {ex.Message}"
               }
            };
         }
      }

      public async Task<List<PublicHolidayRegion>> GetRegions()
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.PublicHolidayRegions.ToListAsync();
         }
      }

   }
}
