﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.EntityFrameworkCore;
using Dapper;
using System.Data;
using CPHI.Spark.Model.Services;
using System.Drawing;
using System.Linq;

namespace CPHI.Spark.DataAccess.DataAccess.AutoPrice
{
    public interface IPublicHolidaysDataAccess
    {
        Task<bool> TodayIsABankHoliday(int regionId);
        Task AddPublicHolidayAsync(DateTime date, string regionName, DealerGroupName dealerGroup);
        Task EditPublicHolidayAsync(int id, DateTime date, string regionName);
        Task RemovePublicHolidayAsync(int id);
        Task<List<PublicHolidayResponse>> GetPublicHolidays();
        Task<List<PublicHolidayRegion>> GetRegions();
    }

    public class PublicHolidaysDataAccess : IPublicHolidaysDataAccess
    {


        private readonly string _connectionString;

        public PublicHolidaysDataAccess(string connectionString)
        {
            this._connectionString = connectionString;
        }


      public async Task<bool> TodayIsABankHoliday(int regionId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var today = DateTime.Now.Date;

            var todayHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date == today);

            // No holidays at any region
            if (todayHoliday == null)
               return false;

            return await db.PublicHolidayRegionMaps
                .AnyAsync(x => x.PublicHolidayId == todayHoliday.Id && x.PublicHolidayRegionId == regionId);
         }
      }

      public async Task<Dictionary<int, bool>> GetBankHolidayDictionaryForRetailerSites(int dealerGroupId, DateTime date)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            // Get today's public holiday for the dealer group
            var publicHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date.Date == date.Date && x.DealerGroup_Id == dealerGroupId);

            // If no holiday today, return all false
            if (publicHoliday == null)
            {
               return await db.RetailerSites
                   .Where(rs => rs.DealerGroup_Id == dealerGroupId)
                   .ToDictionaryAsync(rs => rs.Id, rs => false);
            }

            // Get regionIds that are observing today's holiday
            var activeRegionIds = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == publicHoliday.Id)
                .Select(x => x.PublicHolidayRegionId)
                .ToListAsync();

            // Join RetailerSites -> Sites and determine if their region is in activeRegionIds
            var siteRegionMap = await (
                from rs in db.RetailerSites
                join s in db.Sites on rs.Site_Id equals s.Id
                where rs.DealerGroup_Id == dealerGroupId
                select new
                {
                   RetailerSiteId = rs.Id,
                   IsBankHoliday = activeRegionIds.Contains((int) s.PublicHolidayRegionId)
                }
            ).ToListAsync();

            return siteRegionMap.ToDictionary(x => x.RetailerSiteId, x => x.IsBankHoliday);
         }
      }

      public async Task AddPublicHolidayAsync(DateTime date, string regionName, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find or create the region
            var region = await db.PublicHolidayRegions
                .FirstOrDefaultAsync(x => x.Region == regionName);

            if (region == null)
            {
               region = new PublicHolidayRegion { Region = regionName };
               db.PublicHolidayRegions.Add(region);
               await db.SaveChangesAsync();
            }

            // Check if holiday already exists for this date
            var existingHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date.Date == date.Date);

            if (existingHoliday == null)
            {
               // Create new public holiday
               var publicHoliday = new PublicHoliday
               {
                  Date = date.Date,
                  DealerGroup_Id = (int) dealerGroup 
               };

               db.PublicHolidays.Add(publicHoliday);
               await db.SaveChangesAsync();

               // Create the region mapping
               var regionMap = new PublicHolidayRegionMap
               {
                  PublicHolidayId = publicHoliday.Id,
                  PublicHolidayRegionId = region.Id
               };

               db.PublicHolidayRegionMaps.Add(regionMap);
               await db.SaveChangesAsync();
            }
            else
            {
               // Check if this region is already mapped to the existing holiday
               var existingMapping = await db.PublicHolidayRegionMaps
                   .FirstOrDefaultAsync(x => x.PublicHolidayId == existingHoliday.Id && x.PublicHolidayRegionId == region.Id);

               if (existingMapping == null)
               {
                  // Add the region mapping to existing holiday
                  var regionMap = new PublicHolidayRegionMap
                  {
                     PublicHolidayId = existingHoliday.Id,
                     PublicHolidayRegionId = region.Id
                  };

                  db.PublicHolidayRegionMaps.Add(regionMap);
                  await db.SaveChangesAsync();
               }
            }
         }
      }


      public async Task EditPublicHolidayAsync(int id, DateTime date, string regionName)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the existing public holiday
            var existingHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Id == id);

            if (existingHoliday == null)
            {
               throw new ArgumentException("Public holiday not found");
            }

            // Update the public holiday date
            existingHoliday.Date = date.Date;

            // Find or create the region
            var region = await db.PublicHolidayRegions
                .FirstOrDefaultAsync(x => x.Region == regionName);

            if (region == null)
            {
               region = new PublicHolidayRegion { Region = regionName };
               db.PublicHolidayRegions.Add(region);
               await db.SaveChangesAsync();
            }

            // Remove existing region mappings for this holiday
            var existingMaps = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == id)
                .ToListAsync();
            db.PublicHolidayRegionMaps.RemoveRange(existingMaps);

            // Create new region mapping
            var regionMap = new PublicHolidayRegionMap
            {
               PublicHolidayId = id,
               PublicHolidayRegionId = region.Id
            };

            db.PublicHolidayRegionMaps.Add(regionMap);
            await db.SaveChangesAsync();
         }
      }

      public async Task RemovePublicHolidayAsync(int id)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the existing public holiday
            var existingHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Id == id);

            if (existingHoliday == null)
            {
               throw new ArgumentException("Public holiday not found");
            }

            // Remove region mappings first
            var existingMaps = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == id)
                .ToListAsync();
            db.PublicHolidayRegionMaps.RemoveRange(existingMaps);

            // Remove the public holiday
            db.PublicHolidays.Remove(existingHoliday);
            await db.SaveChangesAsync();
         }
      }

      public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
      {
         try
         {
            using (var db = new CPHIDbContext(_connectionString))
            {
               var holidays = await db.PublicHolidays
                   .Include(x => x.RegionMaps)
                   .ThenInclude(x => x.PublicHolidayRegion)
                   .ToListAsync();

               var result = new List<PublicHolidayResponse>();
               foreach (var holiday in holidays)
               {
                  result.Add(new PublicHolidayResponse
                  {
                     Id = holiday.Id,
                     Date = holiday.Date,
                     DealerGroupId = holiday.DealerGroup_Id,
                     RegionIds = holiday.RegionMaps.Select(x => x.PublicHolidayRegionId).ToList(),
                     Success = true,
                     Message = "Success"
                  });
               }

               return result;
            }
         }
         catch (Exception ex)
         {
            return new List<PublicHolidayResponse>
            {
               new PublicHolidayResponse
               {
                  Success = false,
                  Message = $"Error retrieving public holidays: {ex.Message}"
               }
            };
         }
      }

      public async Task<List<PublicHolidayRegion>> GetRegions()
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.PublicHolidayRegions.ToListAsync();
         }
      }

   }
}
