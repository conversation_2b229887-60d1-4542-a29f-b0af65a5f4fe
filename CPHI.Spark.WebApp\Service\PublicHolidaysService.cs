using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Service
{
    public interface IPublicHolidaysService
    {
        Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request);
    }

    public class PublicHolidaysService : IPublicHolidaysService
    {
        private readonly CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess;

        public PublicHolidaysService(CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess)
        {
            this.publicHolidaysDataAccess = publicHolidaysDataAccess;
        }

        public async Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request)
        {
            return await publicHolidaysDataAccess.AddPublicHoliday(request);
        }

    }
}
