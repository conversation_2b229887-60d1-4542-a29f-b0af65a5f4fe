using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;

namespace CPHI.Spark.WebApp.Service
{
   public interface IPublicHolidaysService
   {
      Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request);
      Task<List<PublicHolidayResponse>> GetPublicHolidays();
      Task<PublicHolidayResponse> UpdatePublicHoliday(int id, PublicHolidayRequest request);
      Task<PublicHolidayResponse> DeletePublicHoliday(int id);
      Task<List<PublicHolidayRegion>> GetRegions();
   }

   public class PublicHolidaysService : IPublicHolidaysService
   {
      private readonly IPublicHolidaysDataAccess publicHolidaysDataAccess;
      private readonly IUserService userService;

      public PublicHolidaysService(IPublicHolidaysDataAccess publicHolidaysDataAccess, IUserService userService)
      {
         this.userService = userService;
         this.publicHolidaysDataAccess = publicHolidaysDataAccess;
      }

      public async Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request)
      {
         try
         {
            // Get region names from region IDs
            var allRegions = await GetRegions();
            var selectedRegions = allRegions.Where(r => request.RegionIds.Contains(r.Id)).ToList();

            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            // Add holiday for each selected region
            foreach (var region in selectedRegions)
            {
               await publicHolidaysDataAccess.AddPublicHolidayAsync(request.Date, region.Region, dealerGroup);
            }

            return new PublicHolidayResponse
            {
               Date = request.Date,
               DealerGroupId = request.DealerGroupId,
               RegionIds = request.RegionIds,
               Success = true,
               Message = "Public holiday added successfully"
            };
         }
         catch (Exception ex)
         {
            return new PublicHolidayResponse
            {
               Success = false,
               Message = ex.Message
            };
         }
      }

      public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
      {
         return await publicHolidaysDataAccess.GetPublicHolidays();
      }

      public async Task<PublicHolidayResponse> UpdatePublicHoliday(int id, PublicHolidayRequest request)
      {
         // Implementation would need to be added to data access layer
         throw new NotImplementedException("Update functionality not yet implemented");
      }

      public async Task<PublicHolidayResponse> DeletePublicHoliday(int id)
      {
         try
         {
            await publicHolidaysDataAccess.RemovePublicHolidayAsync(id);
            return new PublicHolidayResponse
            {
               Success = true,
               Message = "Public holiday deleted successfully"
            };
         }
         catch (Exception ex)
         {
            return new PublicHolidayResponse
            {
               Success = false,
               Message = ex.Message
            };
         }
      }

      public async Task<List<PublicHolidayRegion>> GetRegions()
      {
         return await publicHolidaysDataAccess.GetRegions();
      }

   }
}
