using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace CPHI.Spark.WebApp.Service
{
    public interface IPublicHolidaysService
    {
        Task AddPublicHolidayAsync(DateTime date, string regionName);
        Task EditPublicHolidayAsync(int id, DateTime date, string regionName);
        Task RemovePublicHolidayAsync(int id);
        Task<List<PublicHolidayResponse>> GetPublicHolidays();
    }

    public class PublicHolidaysService : IPublicHolidaysService
    {
        private readonly CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess;

        public PublicHolidaysService(CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess)
        {
            this.publicHolidaysDataAccess = publicHolidaysDataAccess;
        }

        public async Task AddPublicHolidayAsync(DateTime date, string regionName)
        {
            await publicHolidaysDataAccess.AddPublicHolidayAsync(date, regionName);
        }

        public async Task EditPublicHolidayAsync(int id, DateTime date, string regionName)
        {
            await publicHolidaysDataAccess.EditPublicHolidayAsync(id, date, regionName);
        }

        public async Task RemovePublicHolidayAsync(int id)
        {
            await publicHolidaysDataAccess.RemovePublicHolidayAsync(id);
        }

        public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
        {
            return await publicHolidaysDataAccess.GetPublicHolidays();
        }

    }
}
