using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace CPHI.Spark.WebApp.Service
{
    public interface IPublicHolidaysService
    {
        Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request);
        Task<List<PublicHolidayResponse>> GetPublicHolidays();
        Task<PublicHolidayResponse> UpdatePublicHoliday(int id, PublicHolidayRequest request);
        Task<PublicHolidayResponse> DeletePublicHoliday(int id);
    }

    public class PublicHolidaysService : IPublicHolidaysService
    {
        private readonly CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess;

        public PublicHolidaysService(CPHI.Spark.DataAccess.DataAccess.AutoPrice.IPublicHolidaysDataAccess publicHolidaysDataAccess)
        {
            this.publicHolidaysDataAccess = publicHolidaysDataAccess;
        }

        public async Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request)
        {
            return await publicHolidaysDataAccess.AddPublicHoliday(request);
        }

        public async Task<List<PublicHolidayResponse>> GetPublicHolidays()
        {
            return await publicHolidaysDataAccess.GetPublicHolidays();
        }

        public async Task<PublicHolidayResponse> UpdatePublicHoliday(int id, PublicHolidayRequest request)
        {
            // Implementation would need to be added to data access layer
            throw new NotImplementedException("Update functionality not yet implemented");
        }

        public async Task<PublicHolidayResponse> DeletePublicHoliday(int id)
        {
            // Implementation would need to be added to data access layer
            throw new NotImplementedException("Delete functionality not yet implemented");
        }

    }
}
