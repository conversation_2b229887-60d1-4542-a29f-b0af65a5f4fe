import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { ConstantsService } from './services/constants.service';
import { SelectionsService } from './services/selections.service';


@Injectable({
  providedIn: 'root'
})
export class LoggedInGuard implements CanActivate {


  constructor(
    private selections: SelectionsService,
    public constants: ConstantsService,
    private router: Router

  ) { }


  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    if (localStorage.getItem('accessToken')) {
      const urlPath = route.url[0].path;

      //Check access to each route
      if (urlPath == 'dashboard') { 
        return this.selections.user.permissions.seeDashboard;
      } 
      if (urlPath == 'orderBook') { 
        return this.selections.user.permissions.seeOrderbook;
      } 
      if (urlPath == 'dealsDoneThisWeek') { 
        return this.selections.user.permissions.seeDealsDoneThisWeek;
      } 
      if (urlPath == 'dealsForTheMonth') { 
        return this.selections.user.permissions.seeDealsForTheMonth;
      } 
      if (urlPath == 'whiteboard') { 
        return this.selections.user.permissions.seeWhiteboard;
      } 
      if (urlPath == 'performanceLeague') { 
        return this.selections.user.permissions.seePerformanceLeague;
      } 
      if (urlPath == 'performanceTrends') { 
        return this.selections.user.permissions.seePerformanceTrends;
      } 
      if (urlPath == 'handoverDiary') { 
        return this.selections.user.permissions.seeHandoverDiary;
      } 

      if (urlPath == 'scratchCard') { 
        return this.selections.user.permissions.seeScratchCards;
      } 
      if (urlPath == 'userMaintenance') { 
        return this.selections.user.permissions.seeUserMaintenance;
      } 
      if (urlPath == 'stockPricing') { 
        return this.selections.user.permissions.seeStockPricing;
      } 

      if (urlPath == 'stockList') { 
        return this.selections.user.permissions.seeStockList;
      } 

      if (urlPath == 'salesCommission') { //replacement of commissiongaurd
        return (this.selections.user.permissions.reviewCommission || (this.constants.environment.salesmenCanViewWebAppCommission && this.selections.user.permissions.reviewCommission || this.selections.user.permissions.selfOnlyCommission))
      }
      if (urlPath == 'userMaintenance') { //replacement of stocklanding gaurd
        return this.selections.user.RoleName == 'System Administrator';
      }
      if (urlPath == 'publicHolidays') {
        return this.selections.user.RoleName == 'System Administrator';
      }
      if (urlPath == 'stockLanding') { //replacement of stocklanding gaurd
        return this.selections.user.permissions.seeStockLanding;
      }
      if (urlPath == 'reportingCentre') {
        return this.selections.user.permissions.accessReportCentre;
      }
      if (urlPath == 'fleetOrderbook') {
        return this.selections.user.permissions.seeFleetOrderbook;
      }
      if (urlPath == 'stockDashboard') {
        return this.selections.user.permissions.canReviewStockPrices;
      }
      if (urlPath == 'stockOverviewBySite') {
        return this.selections.user.permissions.canReviewStockPrices;
      }
      if (urlPath == 'salesExecReview') {
        return this.selections.user.permissions.salesExecReview !== 'none';
      }
      if (urlPath == 'liveForecastStatus') {
        return this.selections.user.permissions.liveForecast !== 'none' && (this.selections.user.permissions.liveForecast == 'approver' || this.selections.user.permissions.liveForecast == 'submitter');
      }
      if (urlPath == 'liveForecastInput') {
        return this.selections.user.permissions.liveForecast !== 'none' && (this.selections.user.permissions.liveForecast != 'approver' && this.selections.user.permissions.liveForecast != 'submitter');
      }
      return true;
    } else {
      this.router.navigate(['/login']);
    }
    return false;

  }


}

