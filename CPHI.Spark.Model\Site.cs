﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{

    public class Site
    {

        public int Id { get; set; }
        public int Code { get; set; }
        public int SortOrder { get; set; }
        public string Description { get; set; }
        public string DescShort{ get; set; }
        public bool IsRetail { get; set; }
        public bool IsSales { get; set; }
        public bool IsService { get; set; }
        public bool IsParts { get; set; }
        public bool IsActive { get; set; }
        public bool IsRenaultDacia { get; set; }
        public bool IsNissan { get; set; }
        public bool IsAlpine { get; set; }
        //public int Location_Id { get; set; }
        //[ForeignKey("Location_Id")]
        //public Location Location { get; set; }


        public int? Brand_Id { get; set; }
        [ForeignKey("Brand_Id")]
        public Brand Brand { get; set; }

        public int? Region_Id { get; set; }
        [ForeignKey("Region_Id")]
        public Region RegionFK { get; set; }  //need to change this to Region in future

        public int? BusinessUnit_Id { get; set; }
        [ForeignKey("BusinessUnit_Id")]
        public BusinessUnit BusinessUnit { get; set; }  //need to change this to Region in future

        //public int? LBDM_Id { get; set; }
        //[ForeignKey("LBDM_Id")]
        //public Person LBDM { get; set; }  


        public int DealerGroup_Id { get; set; }
        [ForeignKey("DealerGroup_Id")]
        public DealerGroup DealerGroup { get; set; }


        public string GeoX { get; set; }  //This is Latitude
        public string GeoY { get; set; } //This is Longitude
        public int DealerNumber { get; set; }
        public decimal SatOpening { get; set; }
        public decimal SunOpening { get; set; }
        public decimal ServRecRateEffective { get; set; }
        public decimal ServRecRateRetail { get; set; }
        public decimal LoanCars { get; set; }
        public decimal LoanVans { get; set; }
        public decimal DeadPartStockValue { get; set; }
        public decimal DeadPartStockProvn { get; set; }
        public decimal DormantPartStockValue { get; set; }
        public decimal DormantPartStockProvn { get; set; }

        public int BrandDealerId { get; set; }
        public int WebsiteId { get; set; }

        //foreign keys.  One in this connect to many in that
        public virtual ICollection<ServiceTransaction> ServiceTransactions { get; set; }
        public virtual ICollection<TechDailyHoursCount> TechDailyHoursCounts{ get; set; }
        public virtual ICollection<VoCScore> VoCScores { get; set; }

        public int? PublicHolidayRegionId { get; set; }
        [ForeignKey("PublicHolidayRegionId")]
        public PublicHolidayRegion PublicHolidayRegion { get; set; }

    }
}
